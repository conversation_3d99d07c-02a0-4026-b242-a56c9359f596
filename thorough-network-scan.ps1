# Thorough Network Scanner Script
# This script performs a comprehensive scan to find ALL devices on your network

# Function to get MAC vendor information
function Get-MacVendor {
    param (
        [string]$MacAddress
    )

    # Clean up MAC address format
    $MacAddress = $MacAddress -replace "[^0-9A-Fa-f]", ""
    if ($MacAddress.Length -ge 6) {
        $oui = $MacAddress.Substring(0, 6).ToUpper()

        # Common OUI prefixes and their vendors
        $vendors = @{
            "FCFBFB" = "Cisco Systems"
            "00005E" = "IANA"
            "000C29" = "VMware"
            "001801" = "Dell"
            "001A11" = "Google"
            "002272" = "American Micro-Fuel Device Corp"
            "0023DF" = "Apple"
            "002500" = "Apple"
            "00CDFE" = "Apple"
            "04D3CF" = "Apple"
            "04F13E" = "Apple"
            "0C771A" = "Apple"
            "10DDB1" = "Apple"
            "14205E" = "Apple"
            "182032" = "Apple"
            "24A074" = "Apple"
            "24F094" = "Apple"
            "3010E4" = "Apple"
            "3035AD" = "Apple"
            "34C059" = "Apple"
            "40A6D9" = "Apple"
            "44D884" = "Apple"
            "483B38" = "Apple"
            "4C57CA" = "Apple"
            "4CB199" = "Apple"
            "70F087" = "Apple"
            "78D75F" = "Apple"
            "7CD1C3" = "Apple"
            "88C663" = "Apple"
            "8C8FE9" = "Apple"
            "9CE33F" = "Apple"
            "A85B78" = "Apple"
            "A8BBCF" = "Apple"
            "B8C75D" = "Apple"
            "B8FF61" = "Apple"
            "BC9FEF" = "Apple"
            "C82A14" = "Apple"
            "D0E140" = "Apple"
            "D8D1CB" = "Apple"
            "DC2B2A" = "Apple"
            "E0B9BA" = "Apple"
            "E0F847" = "Apple"
            "F431C3" = "Apple"
            "F437B7" = "Apple"
            "F8FF5F" = "Apple"
            "001925" = "Samsung"
            "0023D6" = "Samsung"
            "002490" = "Samsung"
            "0026E4" = "Samsung"
            "5CA39D" = "Samsung"
            "B407F9" = "Samsung"
            "001C23" = "Dell"
            "002219" = "Dell"
            "00219B" = "Dell"
            "000874" = "Dell"
            "001143" = "Dell"
            "001AA0" = "Dell"
            "00065B" = "Dell"
            "18A905" = "Hewlett-Packard"
            "001B78" = "Hewlett-Packard"
            "001E0B" = "Hewlett-Packard"
            "002264" = "Hewlett-Packard"
            "002347" = "Hewlett-Packard"
            "002561" = "Hewlett-Packard"
            "00306E" = "Hewlett-Packard"
            "0014C2" = "Hewlett-Packard"
            "001871" = "Hewlett-Packard"
            "001CC4" = "Hewlett-Packard"
            "001E68" = "Hewlett-Packard"
            "002481" = "Hewlett-Packard"
            "001AA4" = "ARRIS Group"
            "001BDD" = "ARRIS Group"
            "001D6B" = "ARRIS Group"
            "001DBE" = "ARRIS Group"
            "0023A3" = "ARRIS Group"
            "0025F1" = "ARRIS Group"
            "0026BA" = "ARRIS Group"
            "001C11" = "ARRIS Group"
            "001F7E" = "ARRIS Group"
            "0022B0" = "D-Link"
            "00265A" = "D-Link"
            "14D64D" = "D-Link"
            "1C7EE5" = "D-Link"
            "28107B" = "D-Link"
            "3C1E04" = "D-Link"
            "00E04C" = "Realtek"
            "000C76" = "MICRO-STAR INT'L CO.,LTD"
            "001A80" = "Sony Corporation"
            "001D0D" = "Sony Corporation"
            "001FE4" = "Sony Corporation"
            "0022A6" = "Sony Corporation"
            "0013E0" = "Murata Manufacturing Co., Ltd."
            "001F5B" = "Apple"
            "001FF3" = "Apple"
            "0021E9" = "Apple"
            "00236C" = "Apple"
            "0025BC" = "Apple"
            "0026BB" = "Apple"
            "0050E4" = "Apple"
            "0180C2" = "IEEE 802.1 Committee"
            "01000C" = "Cisco Systems"
            "01005E" = "IANA - Multicast"
            "333300" = "IPv6 Multicast"
            "0A0027" = "Oracle/VirtualBox"
            "182649" = "Intel Corporation"
        }

        if ($vendors.ContainsKey($oui)) {
            return $vendors[$oui]
        } else {
            return "Unknown"
        }
    } else {
        return "Unknown"
    }
}

# Function to determine device type based on open ports and other characteristics
function Get-DeviceType {
    param (
        [string]$IPAddress,
        [string]$MacVendor,
        [array]$OpenPorts
    )

    if ($MacVendor -match "Apple") {
        if ($OpenPorts -contains 548 -or $OpenPorts -contains 5009) {
            return "Apple Mac"
        } elseif ($OpenPorts -contains 62078) {
            return "Apple iOS Device (iPhone/iPad)"
        } else {
            return "Apple Device"
        }
    } elseif ($MacVendor -match "Samsung") {
        return "Samsung Device (Phone/TV/Appliance)"
    } elseif ($MacVendor -match "Sony") {
        if ($OpenPorts -contains 1935) {
            return "Sony Smart TV"
        } else {
            return "Sony Device"
        }
    } elseif ($MacVendor -match "ARRIS") {
        return "Cable Modem/Router"
    } elseif ($MacVendor -match "Cisco") {
        return "Cisco Network Device"
    } elseif ($MacVendor -match "D-Link" -or $MacVendor -match "TP-Link") {
        return "Network Router/Switch"
    } elseif ($MacVendor -match "Raspberry") {
        return "Raspberry Pi"
    } elseif ($MacVendor -match "Intel") {
        return "Computer with Intel NIC"
    } elseif ($MacVendor -match "Realtek") {
        return "Computer with Realtek NIC"
    } elseif ($MacVendor -match "Dell") {
        return "Dell Computer"
    } elseif ($MacVendor -match "Hewlett-Packard" -or $MacVendor -match "HP") {
        return "HP Computer/Printer"
    } elseif ($MacVendor -match "VMware") {
        return "Virtual Machine"
    } elseif ($MacVendor -match "Oracle/VirtualBox") {
        return "VirtualBox VM"
    }

    # Check based on open ports
    if ($OpenPorts -contains 80 -or $OpenPorts -contains 443) {
        if ($OpenPorts -contains 8080 -or $OpenPorts -contains 8443) {
            return "Web Server or Router"
        }
    }

    if ($OpenPorts -contains 22) {
        return "Device with SSH (Linux/Network Device)"
    }

    if ($OpenPorts -contains 53) {
        return "DNS Server"
    }

    if ($OpenPorts -contains 445 -or $OpenPorts -contains 139) {
        return "Windows Computer"
    }

    if ($OpenPorts -contains 1883 -or $OpenPorts -contains 8883) {
        return "IoT Device (MQTT)"
    }

    if ($OpenPorts -contains 554) {
        return "IP Camera/Streaming Device"
    }

    return "Unknown Device"
}

# Function to scan a subnet using multiple methods
function Scan-NetworkThoroughly {
    param (
        [string]$Subnet,
        [int]$Timeout = 500
    )

    Write-Host "`nScanning subnet $Subnet thoroughly..." -ForegroundColor Cyan

    # Extract the base IP
    $baseIP = $Subnet.Substring(0, $Subnet.LastIndexOf('.') + 1)

    $results = @()
    $pingTasks = @()

    # Create a runspace pool for parallel scanning
    $runspacePool = [runspacefactory]::CreateRunspacePool(1, 50)
    $runspacePool.Open()

    # Prepare ping tasks for all IPs in the subnet
    1..254 | ForEach-Object {
        $ip = $baseIP + $_

        $pingPS = [powershell]::Create().AddScript({
            param($ip, $timeout)

            $ping = New-Object System.Net.NetworkInformation.Ping
            try {
                $reply = $ping.Send($ip, $timeout)
                if ($reply.Status -eq 'Success') {
                    return @{
                        IPAddress = $ip
                        ResponseTime = $reply.RoundtripTime
                        Method = "ICMP"
                        Status = "Online"
                    }
                }
            } catch {
                # Ignore errors
            }

            # Try TCP port scan for common ports even if ping fails
            $commonPorts = @(80, 443, 22, 445, 139, 53, 8080)
            foreach ($port in $commonPorts) {
                try {
                    $tcpClient = New-Object System.Net.Sockets.TcpClient
                    $connectionTask = $tcpClient.BeginConnect($ip, $port, $null, $null)
                    $wait = $connectionTask.AsyncWaitHandle.WaitOne($timeout)

                    if ($wait) {
                        try {
                            $tcpClient.EndConnect($connectionTask)
                            return @{
                                IPAddress = $ip
                                ResponseTime = 0
                                Method = "TCP Port $port"
                                Status = "Online"
                            }
                        } catch {
                            # Connection failed after initial success
                        }
                    }
                    $tcpClient.Close()
                } catch {
                    # Ignore errors
                }
            }

            return $null
        }).AddArgument($ip).AddArgument($Timeout)

        $pingPS.RunspacePool = $runspacePool

        $pingTasks += @{
            PowerShell = $pingPS
            Result = $pingPS.BeginInvoke()
            IP = $ip
        }
    }

    # Collect ping results
    $discoveredIPs = @{}

    foreach ($task in $pingTasks) {
        $result = $task.PowerShell.EndInvoke($task.Result)
        if ($result) {
            $discoveredIPs[$result.IPAddress] = $result
        }
        $task.PowerShell.Dispose()
    }

    # Also check ARP table for devices that might not respond to ping
    Write-Host "Checking ARP table for additional devices..." -ForegroundColor Cyan
    $arpResults = arp -a

    foreach ($line in $arpResults) {
        if ($line -match "(\d+\.\d+\.\d+\.\d+).*?([0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2})") {
            $ip = $matches[1]
            $mac = $matches[2]

            # Only add if it's in our target subnet
            if ($ip.StartsWith($baseIP) -and -not $discoveredIPs.ContainsKey($ip)) {
                $discoveredIPs[$ip] = @{
                    IPAddress = $ip
                    ResponseTime = 0
                    Method = "ARP Table"
                    Status = "Online"
                }
            }
        }
    }

    # Get additional information for discovered devices
    foreach ($ip in $discoveredIPs.Keys) {
        $device = $discoveredIPs[$ip]

        # Try to get hostname
        try {
            $hostEntry = [System.Net.Dns]::GetHostEntry($ip)
            $hostname = $hostEntry.HostName
        } catch {
            $hostname = "Unknown"
        }

        # Try to get MAC address
        $macAddress = "Unknown"
        $arpResult = arp -a $ip | Where-Object { $_ -match $ip }
        if ($arpResult -match "([0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2}[:-][0-9a-fA-F]{2})") {
            $macAddress = $matches[1]
        }

        # Get MAC vendor
        $macVendor = Get-MacVendor -MacAddress $macAddress

        # Scan for open ports
        $openPorts = @()
        $portsToScan = @(21, 22, 23, 25, 53, 80, 139, 443, 445, 515, 548, 554, 1883, 3306, 3389, 5000, 5009, 8080, 8443, 8883, 9100)

        foreach ($port in $portsToScan) {
            try {
                $tcpClient = New-Object System.Net.Sockets.TcpClient
                $connectionTask = $tcpClient.BeginConnect($ip, $port, $null, $null)
                $wait = $connectionTask.AsyncWaitHandle.WaitOne(200)

                if ($wait) {
                    try {
                        $tcpClient.EndConnect($connectionTask)
                        $openPorts += $port
                    } catch {
                        # Connection failed after initial success
                    }
                }
                $tcpClient.Close()
            } catch {
                # Ignore errors
            }
        }

        # Determine device type
        $deviceType = Get-DeviceType -IPAddress $ip -MacVendor $macVendor -OpenPorts $openPorts

        # Create result object
        $results += [PSCustomObject]@{
            IPAddress = $ip
            Hostname = $hostname
            MACAddress = $macAddress
            MACVendor = $macVendor
            DeviceType = $deviceType
            OpenPorts = ($openPorts | Sort-Object) -join ", "
            DetectionMethod = $device.Method
            ResponseTime = $device.ResponseTime
        }

        Write-Host "Found device: $ip ($hostname) - $deviceType" -ForegroundColor Green
    }

    $runspacePool.Close()
    $runspacePool.Dispose()

    return $results
}

# Main execution
Clear-Host
Write-Host "===== Thorough Network Scanner =====" -ForegroundColor Green
Write-Host "Scanning your network for ALL connected devices..." -ForegroundColor Yellow

# Get all network adapters with IPv4 addresses
$networkAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
Write-Host "Active Network Adapters:" -ForegroundColor Green
$networkAdapters | Format-Table -Property Name, InterfaceDescription, Status, MacAddress -AutoSize

# Get all IP configurations
Write-Host "`nIP Configurations:" -ForegroundColor Green
$ipConfigs = Get-NetIPConfiguration | Where-Object { $_.IPv4Address -ne $null }
$ipConfigs | Format-Table -Property InterfaceAlias, IPv4Address, IPv4DefaultGateway -AutoSize

# Scan each subnet
$allDevices = @()

foreach ($ipConfig in $ipConfigs) {
    $ipAddress = $ipConfig.IPv4Address.IPAddress
    $subnet = $ipAddress.Substring(0, $ipAddress.LastIndexOf('.') + 1) + "0"

    $devices = Scan-NetworkThoroughly -Subnet $subnet
    $allDevices += $devices
}

# Display results
Write-Host "`n===== All Connected Devices =====" -ForegroundColor Green
$allDevices | Format-Table -Property IPAddress, Hostname, DeviceType, MACAddress, MACVendor, OpenPorts, DetectionMethod -AutoSize

Write-Host "`nScan completed. Found $($allDevices.Count) devices on your network." -ForegroundColor Green
