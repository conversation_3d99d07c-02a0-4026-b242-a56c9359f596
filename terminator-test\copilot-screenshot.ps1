# PowerShell script to open Windows Copilot and take a screenshot

# Add reference to Windows Forms for SendKeys
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class KeyboardSend
{
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    public const int KEYEVENTF_EXTENDEDKEY = 0x0001;
    public const int KEYEVENTF_KEYUP = 0x0002;
    
    public const byte VK_LWIN = 0x5B;  // Left Windows key
    public const byte VK_C = 0x43;     // C key
    public const byte VK_RETURN = 0x0D; // Enter key
    
    public static void SendWinC()
    {
        // Press Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Press C key
        keybd_event(VK_C, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release C key
        keybd_event(VK_C, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static void SendEnter()
    {
        // Press Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
}
"@

# Function to take a screenshot
function Take-Screenshot {
    param (
        [string]$FilePath
    )
    
    $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
    $bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.Size)
    $bitmap.Save($FilePath)
    $graphics.Dispose()
    $bitmap.Dispose()
    
    Write-Host "Screenshot saved to $FilePath"
}

Write-Host "Taking initial screenshot..."
Take-Screenshot -FilePath "terminator-test\before_copilot.png"

Write-Host "Attempting to open Windows Copilot using Win+C shortcut..."
[KeyboardSend]::SendWinC()

# Wait for Copilot to open
Write-Host "Waiting for Copilot to open..."
Start-Sleep -Seconds 3

# Take a screenshot after Copilot opens
Write-Host "Taking screenshot after Copilot opens..."
Take-Screenshot -FilePath "terminator-test\copilot_open.png"

# Type a message
Write-Host "Typing a message..."
$message = "Hello Copilot! This message was sent automatically using PowerShell."
[System.Windows.Forms.SendKeys]::SendWait($message)

# Take a screenshot after typing the message
Write-Host "Taking screenshot after typing message..."
Take-Screenshot -FilePath "terminator-test\message_typed.png"

# Send Enter to submit the message
Start-Sleep -Milliseconds 500
[KeyboardSend]::SendEnter()

# Wait for response
Write-Host "Waiting for response..."
Start-Sleep -Seconds 5

# Take a final screenshot
Write-Host "Taking final screenshot..."
Take-Screenshot -FilePath "terminator-test\final_result.png"

Write-Host "All screenshots saved to terminator-test folder!"
