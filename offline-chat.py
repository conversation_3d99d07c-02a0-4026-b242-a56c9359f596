#!/usr/bin/env python3
"""
Offline AI Chat Simulator - No API required
"""

import os
import sys

# Define pre-programmed responses
AI_RESPONSES = {
    "hello": "Hello! I'm a simulated AI assistant. How can I help you with AI-related topics today?",
    "what is ai": "Artificial Intelligence (AI) refers to systems designed to perform tasks that typically require human intelligence. These include learning, reasoning, problem-solving, perception, and language understanding.",
    "types of ai": "AI can be categorized as: 1) Narrow/Weak AI (designed for specific tasks), 2) General/Strong AI (hypothetical human-like intelligence), 3) Based on techniques (rule-based, machine learning, deep learning, etc.)",
    "machine learning": "Machine Learning is a subset of AI that enables systems to learn from data without explicit programming. Types include supervised learning, unsupervised learning, and reinforcement learning.",
    "ethics": "AI ethics concerns include: bias and fairness, transparency, privacy, accountability, job displacement, safety, and autonomy. These require multidisciplinary approaches.",
    "future of ai": "The future of AI may include more capable foundation models, multimodal AI, personalization, human-AI collaboration, specialized hardware, democratization, regulation, and scientific breakthroughs.",
    "risks": "AI risks include bias, privacy concerns, security vulnerabilities, job displacement, power concentration, autonomous weapons, misinformation, overreliance, and long-term control challenges.",
    "benefits": "AI benefits include advances in healthcare, climate solutions, productivity, accessibility, education, scientific discovery, safety improvements, creative tools, and economic growth.",
    "agi": "Artificial General Intelligence (AGI) refers to hypothetical AI with human-like general intelligence across diverse domains. We don't have AGI today, and there's no consensus on when it might be achieved."
}

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_ai_response(query):
    """Get a response based on the user's query."""
    query_lower = query.lower()
    
    # Check for exact matches
    if query_lower in AI_RESPONSES:
        return AI_RESPONSES[query_lower]
    
    # Check for partial matches
    for key in AI_RESPONSES:
        if key in query_lower or query_lower in key:
            return AI_RESPONSES[key]
    
    # Default response if no match
    return "I'm a simulated AI assistant with limited responses. I can discuss basic AI concepts like 'what is AI', 'types of AI', 'machine learning', 'ethics', 'future of AI', 'risks', 'benefits', and 'AGI'. Please try asking about one of these topics."

def main():
    """Main function to run the chat client."""
    clear_screen()
    print("========================================")
    print("       Offline AI Chat Simulator        ")
    print("========================================")
    print("This is a simple simulation of an AI chat about artificial intelligence topics.")
    print("Type 'exit' or 'quit' to end the conversation")
    print("Try asking about: 'what is AI', 'types of AI', 'machine learning', 'ethics',")
    print("                  'future of AI', 'risks', 'benefits', or 'AGI'")
    print("========================================\n")
    
    # Initial greeting
    print("AI: Hello! I'm a simulated AI assistant focused on AI topics. How can I help you today?\n")
    
    # Main chat loop
    while True:
        try:
            # Get user input
            user_input = input("You: ")
            
            # Check for exit command
            if user_input.lower() in ['exit', 'quit']:
                print("\nAI: Goodbye! Thanks for chatting.")
                break
            
            # Get and print AI response
            ai_response = get_ai_response(user_input)
            print(f"\nAI: {ai_response}\n")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"\nError: {e}")

if __name__ == "__main__":
    main()
