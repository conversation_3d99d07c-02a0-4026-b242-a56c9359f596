# Windows Control Center

A powerful application for controlling Windows system and applications.

## Features

### Running Applications Management
- View all running applications with window titles
- Filter applications by name or window title
- Bring applications to front
- Minimize, maximize, or close applications

### System Information
- Real-time CPU information and usage monitoring
- Memory usage statistics
- Operating system details and uptime

### Services Management
- View and filter all Windows services
- Start, stop, and restart services
- Change service startup type (Automatic, Manual, Disabled)
- Search services by name or description
- Filter services by status or startup type

### Network Management
- View and manage network adapters
- Enable/disable network adapters
- Renew DHCP leases
- View detailed adapter information
- Monitor network traffic statistics
- Flush DNS cache
- Ping tool with detailed results

### Automation
- Record keyboard and mouse actions
- Save and load macros
- Play back recorded actions (demo only)

### Task Scheduler
- View scheduled tasks
- Create, edit, and delete tasks (demo only)
- Run tasks on demand

## Building the Application

### Prerequisites
- .NET 6.0 SDK or later
- Visual Studio 2019/2022 or Visual Studio Code

### Build Steps

#### Using Visual Studio
1. Open the `WindowsControlCenter.csproj` file in Visual Studio
2. Build the solution (F6 or Build > Build Solution)
3. Run the application (F5 or Debug > Start Debugging)

#### Using Command Line
1. Navigate to the project directory
2. Run `dotnet build`
3. Run `dotnet run`

## Creating the App Icon
1. Run PowerShell as administrator
2. Navigate to the project directory
3. Run `.\CreateAppIcon.ps1`
4. The script will create an `app.ico` file in the project directory

## Future Enhancements

This application demonstrates core functionality. Future versions could include:

1. **Enhanced System Control**
   - Power management (sleep, hibernate, shutdown scheduling)
   - Registry editor and management
   - Disk management and cleanup
   - System performance monitoring

2. **Advanced Automation**
   - Full macro playback implementation
   - Conditional actions and branching
   - Scheduled automation
   - UI element recognition and interaction

3. **UI Improvements**
   - Customizable dashboard with widgets
   - Dark/light theme support
   - Performance optimizations
   - Multi-monitor support

4. **Security Features**
   - User authentication
   - Permission management
   - Secure storage for sensitive operations
   - System security scanning

5. **Advanced Services Management**
   - Service dependency visualization
   - Service performance monitoring
   - Custom service creation
   - Service recovery options

6. **Advanced Network Management**
   - Network traffic monitoring in real-time
   - Firewall configuration
   - Port scanning and monitoring
   - Network troubleshooting wizards
   - Advanced network diagnostics

## Technical Notes

- The application uses WPF for the user interface
- System information is retrieved using WMI (Windows Management Instrumentation)
- Window manipulation is performed using Win32 API calls
- Keyboard and mouse hooks use low-level Windows hooks
- Service management uses System.ServiceProcess and WMI
- Asynchronous operations are implemented using Task-based Asynchronous Pattern (TAP)
- The application follows MVVM-inspired architecture for separation of concerns

## Limitations

This application has the following limitations:

- Macro playback is not fully implemented
- Task scheduler integration is simulated
- Many features require administrative privileges:
  - Service management (start, stop, change startup type)
  - Some system information retrieval
  - Task scheduling
- Not all system information is available
- UI is not fully responsive for different screen sizes
- Limited error handling for edge cases

## License

This project is licensed under the MIT License - see the LICENSE file for details.
