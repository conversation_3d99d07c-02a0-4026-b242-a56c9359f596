using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using Microsoft.Win32;

namespace WindowsControlCenter
{
    public partial class MainWindow : Window
    {
        // Win32 API imports for window manipulation
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        // ShowWindow constants
        private const int SW_MINIMIZE = 6;
        private const int SW_MAXIMIZE = 3;
        private const int SW_RESTORE = 9;

        // Observable collections for data binding
        private ObservableCollection<ProcessInfo> _processes;
        private ObservableCollection<ServiceInfo> _services;
        private ObservableCollection<NetworkAdapterInfo> _networkAdapters;
        private ObservableCollection<PingResultItemDisplay> _pingResults;
        private ObservableCollection<MacroAction> _macroActions;
        private ObservableCollection<ScheduledTask> _scheduledTasks;

        // Timer for updating system information
        private DispatcherTimer _systemInfoTimer;

        // Macro recording state
        private bool _isRecording = false;
        private DateTime _recordingStartTime;
        private LowLevelKeyboardHook _keyboardHook;
        private LowLevelMouseHook _mouseHook;

        public MainWindow()
        {
            InitializeComponent();

            // Initialize collections
            _processes = new ObservableCollection<ProcessInfo>();
            ApplicationsListView.ItemsSource = _processes;

            _services = new ObservableCollection<ServiceInfo>();
            ServicesListView.ItemsSource = _services;

            _networkAdapters = new ObservableCollection<NetworkAdapterInfo>();
            NetworkAdaptersListView.ItemsSource = _networkAdapters;

            _pingResults = new ObservableCollection<PingResultItemDisplay>();
            PingResultsListView.ItemsSource = _pingResults;

            _macroActions = new ObservableCollection<MacroAction>();
            MacroActionsListView.ItemsSource = _macroActions;

            _scheduledTasks = new ObservableCollection<ScheduledTask>();
            ScheduledTasksListView.ItemsSource = _scheduledTasks;

            // Initialize system info timer
            _systemInfoTimer = new DispatcherTimer();
            _systemInfoTimer.Interval = TimeSpan.FromSeconds(2);
            _systemInfoTimer.Tick += SystemInfoTimer_Tick;

            // Initialize hooks for macro recording
            _keyboardHook = new LowLevelKeyboardHook();
            _keyboardHook.KeyDown += KeyboardHook_KeyDown;

            _mouseHook = new LowLevelMouseHook();
            _mouseHook.MouseEvent += MouseHook_MouseEvent;

            // Load initial data
            LoadRunningApplications();
            LoadSystemInformation();
            LoadServices();
            LoadNetworkAdapters();
            LoadNetworkStatistics();
            LoadScheduledTasks();

            // Check for administrator privileges
            if (!ServiceManager.IsAdministrator())
            {
                AdminWarningTextBlock.Visibility = Visibility.Visible;
                NetworkAdminWarningTextBlock.Visibility = Visibility.Visible;
            }

            // Start system info timer
            _systemInfoTimer.Start();
        }

        #region Running Applications Tab

        private void LoadRunningApplications()
        {
            _processes.Clear();

            foreach (var process in Process.GetProcesses())
            {
                if (!string.IsNullOrEmpty(process.MainWindowTitle))
                {
                    _processes.Add(new ProcessInfo
                    {
                        Id = process.Id,
                        ProcessName = process.ProcessName,
                        MainWindowTitle = process.MainWindowTitle,
                        MemoryMB = Math.Round(process.WorkingSet64 / 1024.0 / 1024.0, 2)
                    });
                }
            }
        }

        private void RefreshAppsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadRunningApplications();
        }

        private void FilterTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            string filter = FilterTextBox.Text.ToLower();

            if (string.IsNullOrEmpty(filter))
            {
                LoadRunningApplications();
                return;
            }

            var filteredProcesses = Process.GetProcesses()
                .Where(p => !string.IsNullOrEmpty(p.MainWindowTitle) &&
                           (p.ProcessName.ToLower().Contains(filter) ||
                            p.MainWindowTitle.ToLower().Contains(filter)))
                .Select(p => new ProcessInfo
                {
                    Id = p.Id,
                    ProcessName = p.ProcessName,
                    MainWindowTitle = p.MainWindowTitle,
                    MemoryMB = Math.Round(p.WorkingSet64 / 1024.0 / 1024.0, 2)
                });

            _processes.Clear();
            foreach (var process in filteredProcesses)
            {
                _processes.Add(process);
            }
        }

        private void ApplicationsListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = ApplicationsListView.SelectedItem != null;
            BringToFrontButton.IsEnabled = hasSelection;
            MinimizeButton.IsEnabled = hasSelection;
            MaximizeButton.IsEnabled = hasSelection;
            CloseAppButton.IsEnabled = hasSelection;
        }

        private void BringToFrontButton_Click(object sender, RoutedEventArgs e)
        {
            if (ApplicationsListView.SelectedItem is ProcessInfo selectedProcess)
            {
                try
                {
                    Process process = Process.GetProcessById(selectedProcess.Id);
                    SetForegroundWindow(process.MainWindowHandle);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error bringing window to front: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (ApplicationsListView.SelectedItem is ProcessInfo selectedProcess)
            {
                try
                {
                    Process process = Process.GetProcessById(selectedProcess.Id);
                    ShowWindow(process.MainWindowHandle, SW_MINIMIZE);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error minimizing window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (ApplicationsListView.SelectedItem is ProcessInfo selectedProcess)
            {
                try
                {
                    Process process = Process.GetProcessById(selectedProcess.Id);
                    ShowWindow(process.MainWindowHandle, SW_MAXIMIZE);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error maximizing window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CloseAppButton_Click(object sender, RoutedEventArgs e)
        {
            if (ApplicationsListView.SelectedItem is ProcessInfo selectedProcess)
            {
                if (MessageBox.Show($"Are you sure you want to close {selectedProcess.ProcessName}?",
                                   "Confirm Close", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    try
                    {
                        Process process = Process.GetProcessById(selectedProcess.Id);
                        process.CloseMainWindow();

                        // Wait a bit and refresh the list
                        Task.Delay(500).ContinueWith(_ =>
                        {
                            Dispatcher.Invoke(() => LoadRunningApplications());
                        });
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error closing application: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        #endregion

        #region System Information Tab

        private void LoadSystemInformation()
        {
            // Load CPU information
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        CpuNameTextBlock.Text = obj["Name"].ToString();
                        CpuCoresTextBlock.Text = obj["NumberOfCores"].ToString();

                        int cpuLoad = Convert.ToInt32(obj["LoadPercentage"]);
                        CpuUsageProgressBar.Value = cpuLoad;

                        // Set color based on load
                        if (cpuLoad < 60)
                            CpuUsageProgressBar.Foreground = System.Windows.Media.Brushes.Green;
                        else if (cpuLoad < 85)
                            CpuUsageProgressBar.Foreground = System.Windows.Media.Brushes.Orange;
                        else
                            CpuUsageProgressBar.Foreground = System.Windows.Media.Brushes.Red;

                        break; // Just take the first CPU
                    }
                }
            }
            catch (Exception ex)
            {
                CpuNameTextBlock.Text = "Error: " + ex.Message;
            }

            // Load memory information
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        ulong totalMemory = Convert.ToUInt64(obj["TotalVisibleMemorySize"]);
                        ulong freeMemory = Convert.ToUInt64(obj["FreePhysicalMemory"]);

                        double totalMemoryGB = Math.Round(totalMemory / 1024.0 / 1024.0, 2);
                        double freeMemoryGB = Math.Round(freeMemory / 1024.0 / 1024.0, 2);
                        double usedMemoryGB = totalMemoryGB - freeMemoryGB;

                        MemoryTotalTextBlock.Text = $"{totalMemoryGB} GB";
                        MemoryAvailableTextBlock.Text = $"{freeMemoryGB} GB ({Math.Round(freeMemoryGB / totalMemoryGB * 100, 1)}%)";

                        double memoryUsagePercent = (double)(totalMemory - freeMemory) / totalMemory * 100;
                        MemoryUsageProgressBar.Value = memoryUsagePercent;

                        // Set color based on usage
                        if (memoryUsagePercent < 60)
                            MemoryUsageProgressBar.Foreground = System.Windows.Media.Brushes.Green;
                        else if (memoryUsagePercent < 85)
                            MemoryUsageProgressBar.Foreground = System.Windows.Media.Brushes.Orange;
                        else
                            MemoryUsageProgressBar.Foreground = System.Windows.Media.Brushes.Red;

                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                MemoryTotalTextBlock.Text = "Error: " + ex.Message;
            }

            // Load OS information
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        OsNameTextBlock.Text = obj["Caption"].ToString();
                        OsVersionTextBlock.Text = $"{obj["Version"]} (Build {obj["BuildNumber"]})";

                        // Calculate uptime
                        var lastBootTime = ManagementDateTimeConverter.ToDateTime(obj["LastBootUpTime"].ToString());
                        var uptime = DateTime.Now - lastBootTime;
                        OsUptimeTextBlock.Text = $"{uptime.Days} days, {uptime.Hours} hours, {uptime.Minutes} minutes";

                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                OsNameTextBlock.Text = "Error: " + ex.Message;
            }
        }

        private void RefreshSystemInfoButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSystemInformation();
        }

        private void SystemInfoTimer_Tick(object sender, EventArgs e)
        {
            LoadSystemInformation();
        }

        #endregion

        #region Automation Tab

        private void StartRecordingButton_Click(object sender, RoutedEventArgs e)
        {
            _isRecording = true;
            _recordingStartTime = DateTime.Now;
            _macroActions.Clear();

            // Update UI
            StartRecordingButton.IsEnabled = false;
            StopRecordingButton.IsEnabled = true;
            PlayMacroButton.IsEnabled = false;
            SaveMacroButton.IsEnabled = false;

            // Start hooks
            _keyboardHook.Install();
            _mouseHook.Install();

            // Add recording start action
            _macroActions.Add(new MacroAction
            {
                ActionType = "System",
                Details = "Recording started",
                Timestamp = DateTime.Now.ToString("HH:mm:ss.fff")
            });
        }

        private void StopRecordingButton_Click(object sender, RoutedEventArgs e)
        {
            _isRecording = false;

            // Update UI
            StartRecordingButton.IsEnabled = true;
            StopRecordingButton.IsEnabled = false;
            PlayMacroButton.IsEnabled = _macroActions.Count > 0;
            SaveMacroButton.IsEnabled = _macroActions.Count > 0;

            // Stop hooks
            _keyboardHook.Uninstall();
            _mouseHook.Uninstall();

            // Add recording stop action
            _macroActions.Add(new MacroAction
            {
                ActionType = "System",
                Details = "Recording stopped",
                Timestamp = DateTime.Now.ToString("HH:mm:ss.fff")
            });
        }

        private void KeyboardHook_KeyDown(object sender, KeyEventArgs e)
        {
            if (_isRecording)
            {
                _macroActions.Add(new MacroAction
                {
                    ActionType = "Keyboard",
                    Details = $"Key: {e.Key}",
                    Timestamp = DateTime.Now.ToString("HH:mm:ss.fff")
                });
            }
        }

        private void MouseHook_MouseEvent(object sender, MouseEventArgs e)
        {
            if (_isRecording)
            {
                string details = $"Button: {e.ChangedButton}, Position: ({e.GetPosition(null).X}, {e.GetPosition(null).Y})";

                _macroActions.Add(new MacroAction
                {
                    ActionType = "Mouse",
                    Details = details,
                    Timestamp = DateTime.Now.ToString("HH:mm:ss.fff")
                });
            }
        }

        private void PlayMacroButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Macro playback is not implemented in this demo.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SaveMacroButton_Click(object sender, RoutedEventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Filter = "Macro Files (*.macro)|*.macro",
                DefaultExt = "macro",
                Title = "Save Macro"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                // In a real application, we would serialize the macro actions to the file
                MessageBox.Show($"Macro would be saved to: {saveFileDialog.FileName}", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void LoadMacroButton_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "Macro Files (*.macro)|*.macro",
                Title = "Load Macro"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                // In a real application, we would deserialize the macro actions from the file
                MessageBox.Show($"Macro would be loaded from: {openFileDialog.FileName}", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ClearMacroButton_Click(object sender, RoutedEventArgs e)
        {
            _macroActions.Clear();
            PlayMacroButton.IsEnabled = false;
            SaveMacroButton.IsEnabled = false;
        }

        #endregion

        #region Services Tab

        private void LoadServices()
        {
            try
            {
                Mouse.OverrideCursor = Cursors.Wait;

                _services.Clear();
                var services = ServiceManager.GetAllServices();

                foreach (var service in services)
                {
                    _services.Add(service);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading services: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private void FilterServices()
        {
            try
            {
                Mouse.OverrideCursor = Cursors.Wait;

                string searchText = ServiceSearchTextBox.Text.ToLower();
                ComboBoxItem selectedFilter = ServiceFilterComboBox.SelectedItem as ComboBoxItem;
                string filterText = selectedFilter?.Content.ToString() ?? "All Services";

                var services = ServiceManager.GetAllServices();

                // Apply search filter
                if (!string.IsNullOrEmpty(searchText))
                {
                    services = services.Where(s =>
                        s.DisplayName.ToLower().Contains(searchText) ||
                        s.Name.ToLower().Contains(searchText) ||
                        s.Description.ToLower().Contains(searchText)).ToList();
                }

                // Apply category filter
                switch (filterText)
                {
                    case "Running Only":
                        services = services.Where(s => s.Status == "Running").ToList();
                        break;
                    case "Stopped Only":
                        services = services.Where(s => s.Status == "Stopped").ToList();
                        break;
                    case "Automatic Startup":
                        services = services.Where(s => s.StartupType == "Automatic").ToList();
                        break;
                    case "Manual Startup":
                        services = services.Where(s => s.StartupType == "Manual").ToList();
                        break;
                    case "Disabled":
                        services = services.Where(s => s.StartupType == "Disabled").ToList();
                        break;
                }

                _services.Clear();
                foreach (var service in services)
                {
                    _services.Add(service);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering services: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private void RefreshServicesButton_Click(object sender, RoutedEventArgs e)
        {
            LoadServices();
        }

        private void ServiceFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterServices();
        }

        private void ServiceSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterServices();
        }

        private void ServicesListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = ServicesListView.SelectedItem != null;
            StartServiceButton.IsEnabled = hasSelection;
            StopServiceButton.IsEnabled = hasSelection;
            RestartServiceButton.IsEnabled = hasSelection;
            StartupTypeComboBox.IsEnabled = hasSelection;

            if (hasSelection && ServicesListView.SelectedItem is ServiceInfo selectedService)
            {
                // Set the startup type combobox to match the service's current startup type
                switch (selectedService.StartupType)
                {
                    case "Automatic":
                        StartupTypeComboBox.SelectedIndex = 0;
                        break;
                    case "Manual":
                        StartupTypeComboBox.SelectedIndex = 1;
                        break;
                    case "Disabled":
                        StartupTypeComboBox.SelectedIndex = 2;
                        break;
                    default:
                        StartupTypeComboBox.SelectedIndex = -1;
                        break;
                }
            }
        }

        private async void StartServiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (ServicesListView.SelectedItem is ServiceInfo selectedService)
            {
                try
                {
                    Mouse.OverrideCursor = Cursors.Wait;

                    await ServiceManager.StartServiceAsync(selectedService.Name);

                    // Refresh the services list
                    LoadServices();

                    MessageBox.Show($"Service '{selectedService.DisplayName}' started successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (UnauthorizedAccessException)
                {
                    MessageBox.Show("Administrator privileges are required to start services. Please run the application as administrator.",
                                   "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error starting service: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    Mouse.OverrideCursor = null;
                }
            }
        }

        private async void StopServiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (ServicesListView.SelectedItem is ServiceInfo selectedService)
            {
                if (MessageBox.Show($"Are you sure you want to stop the service '{selectedService.DisplayName}'?",
                                   "Confirm Stop", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    try
                    {
                        Mouse.OverrideCursor = Cursors.Wait;

                        await ServiceManager.StopServiceAsync(selectedService.Name);

                        // Refresh the services list
                        LoadServices();

                        MessageBox.Show($"Service '{selectedService.DisplayName}' stopped successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        MessageBox.Show("Administrator privileges are required to stop services. Please run the application as administrator.",
                                       "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error stopping service: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        Mouse.OverrideCursor = null;
                    }
                }
            }
        }

        private async void RestartServiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (ServicesListView.SelectedItem is ServiceInfo selectedService)
            {
                if (MessageBox.Show($"Are you sure you want to restart the service '{selectedService.DisplayName}'?",
                                   "Confirm Restart", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    try
                    {
                        Mouse.OverrideCursor = Cursors.Wait;

                        await ServiceManager.RestartServiceAsync(selectedService.Name);

                        // Refresh the services list
                        LoadServices();

                        MessageBox.Show($"Service '{selectedService.DisplayName}' restarted successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        MessageBox.Show("Administrator privileges are required to restart services. Please run the application as administrator.",
                                       "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error restarting service: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        Mouse.OverrideCursor = null;
                    }
                }
            }
        }

        private async void StartupTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ServicesListView.SelectedItem is ServiceInfo selectedService && StartupTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string newStartupType = selectedItem.Content.ToString();

                // Skip if the startup type hasn't changed
                if (selectedService.StartupType == newStartupType)
                    return;

                if (MessageBox.Show($"Are you sure you want to change the startup type of '{selectedService.DisplayName}' to {newStartupType}?",
                                   "Confirm Change", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    try
                    {
                        Mouse.OverrideCursor = Cursors.Wait;

                        await ServiceManager.ChangeStartupTypeAsync(selectedService.Name, newStartupType);

                        // Refresh the services list
                        LoadServices();

                        MessageBox.Show($"Startup type for service '{selectedService.DisplayName}' changed to {newStartupType} successfully.",
                                       "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        MessageBox.Show("Administrator privileges are required to change service configuration. Please run the application as administrator.",
                                       "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error changing startup type: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        Mouse.OverrideCursor = null;
                    }
                }
                else
                {
                    // Revert the selection if the user cancels
                    switch (selectedService.StartupType)
                    {
                        case "Automatic":
                            StartupTypeComboBox.SelectedIndex = 0;
                            break;
                        case "Manual":
                            StartupTypeComboBox.SelectedIndex = 1;
                            break;
                        case "Disabled":
                            StartupTypeComboBox.SelectedIndex = 2;
                            break;
                    }
                }
            }
        }

        #endregion

        #region Network Tab

        private void LoadNetworkAdapters()
        {
            try
            {
                Mouse.OverrideCursor = Cursors.Wait;

                _networkAdapters.Clear();
                var adapters = NetworkManager.GetNetworkAdapters();

                foreach (var adapter in adapters)
                {
                    _networkAdapters.Add(adapter);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading network adapters: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private void LoadNetworkStatistics()
        {
            try
            {
                var stats = NetworkManager.GetNetworkStatistics();

                // Update UI
                BytesSentTextBlock.Text = stats.BytesSentFormatted;
                BytesReceivedTextBlock.Text = stats.BytesReceivedFormatted;
                PacketsSentTextBlock.Text = stats.TotalPacketsSent.ToString("N0");
                PacketsReceivedTextBlock.Text = stats.TotalPacketsReceived.ToString("N0");
                NetworkErrorsTextBlock.Text = stats.TotalErrors.ToString("N0");
                ActiveConnectionsTextBlock.Text = stats.ActiveConnections.ToString("N0");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading network statistics: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshAdaptersButton_Click(object sender, RoutedEventArgs e)
        {
            LoadNetworkAdapters();
        }

        private void NetworkAdaptersListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            bool hasSelection = NetworkAdaptersListView.SelectedItem != null;
            EnableAdapterButton.IsEnabled = hasSelection;
            DisableAdapterButton.IsEnabled = hasSelection;
            RenewDhcpButton.IsEnabled = hasSelection;
            AdapterDetailsButton.IsEnabled = hasSelection;
        }

        private async void EnableAdapterButton_Click(object sender, RoutedEventArgs e)
        {
            if (NetworkAdaptersListView.SelectedItem is NetworkAdapterInfo selectedAdapter)
            {
                try
                {
                    Mouse.OverrideCursor = Cursors.Wait;

                    await NetworkManager.SetNetworkAdapterStatusAsync(selectedAdapter.Id, true);

                    // Refresh the adapters list
                    LoadNetworkAdapters();

                    MessageBox.Show($"Network adapter '{selectedAdapter.Name}' enabled successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (UnauthorizedAccessException)
                {
                    MessageBox.Show("Administrator privileges are required to enable network adapters. Please run the application as administrator.",
                                   "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error enabling network adapter: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    Mouse.OverrideCursor = null;
                }
            }
        }

        private async void DisableAdapterButton_Click(object sender, RoutedEventArgs e)
        {
            if (NetworkAdaptersListView.SelectedItem is NetworkAdapterInfo selectedAdapter)
            {
                if (MessageBox.Show($"Are you sure you want to disable the network adapter '{selectedAdapter.Name}'?",
                                   "Confirm Disable", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    try
                    {
                        Mouse.OverrideCursor = Cursors.Wait;

                        await NetworkManager.SetNetworkAdapterStatusAsync(selectedAdapter.Id, false);

                        // Refresh the adapters list
                        LoadNetworkAdapters();

                        MessageBox.Show($"Network adapter '{selectedAdapter.Name}' disabled successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        MessageBox.Show("Administrator privileges are required to disable network adapters. Please run the application as administrator.",
                                       "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error disabling network adapter: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        Mouse.OverrideCursor = null;
                    }
                }
            }
        }

        private async void RenewDhcpButton_Click(object sender, RoutedEventArgs e)
        {
            if (NetworkAdaptersListView.SelectedItem is NetworkAdapterInfo selectedAdapter)
            {
                try
                {
                    Mouse.OverrideCursor = Cursors.Wait;

                    await NetworkManager.RenewDhcpLeaseAsync(selectedAdapter.Id);

                    // Refresh the adapters list
                    LoadNetworkAdapters();

                    MessageBox.Show($"DHCP lease for network adapter '{selectedAdapter.Name}' renewed successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (UnauthorizedAccessException)
                {
                    MessageBox.Show("Administrator privileges are required to renew DHCP lease. Please run the application as administrator.",
                                   "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error renewing DHCP lease: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    Mouse.OverrideCursor = null;
                }
            }
        }

        private void AdapterDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (NetworkAdaptersListView.SelectedItem is NetworkAdapterInfo selectedAdapter)
            {
                StringBuilder details = new StringBuilder();
                details.AppendLine($"Name: {selectedAdapter.Name}");
                details.AppendLine($"Description: {selectedAdapter.Description}");
                details.AppendLine($"Type: {selectedAdapter.Type}");
                details.AppendLine($"Status: {selectedAdapter.Status}");
                details.AppendLine($"MAC Address: {selectedAdapter.MacAddress}");
                details.AppendLine($"IPv4 Address: {selectedAdapter.IPv4Address}");
                details.AppendLine($"Subnet Mask: {selectedAdapter.SubnetMask}");
                details.AppendLine($"Gateway: {selectedAdapter.Gateway}");
                details.AppendLine($"DNS Servers: {selectedAdapter.DnsServers}");
                details.AppendLine($"Speed: {selectedAdapter.Speed}");

                MessageBox.Show(details.ToString(), $"Network Adapter Details - {selectedAdapter.Name}", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void RefreshNetworkStatsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadNetworkStatistics();
        }

        private async void FlushDnsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Mouse.OverrideCursor = Cursors.Wait;

                await NetworkManager.FlushDnsAsync();

                MessageBox.Show("DNS cache flushed successfully.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (UnauthorizedAccessException)
            {
                MessageBox.Show("Administrator privileges are required to flush DNS cache. Please run the application as administrator.",
                               "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error flushing DNS cache: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private async void StartPingButton_Click(object sender, RoutedEventArgs e)
        {
            string host = PingHostTextBox.Text.Trim();

            if (string.IsNullOrEmpty(host))
            {
                MessageBox.Show("Please enter a host to ping.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                // Get ping count from combo box
                int pingCount = 4;
                if (PingCountComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    pingCount = int.Parse(selectedItem.Content.ToString());
                }

                // Clear previous results
                _pingResults.Clear();

                // Reset result fields
                PingHostResultTextBlock.Text = host;
                PingSuccessRateTextBlock.Text = "Pinging...";
                PingTimesTextBlock.Text = "Pinging...";
                PingStatusTextBlock.Text = "Pinging...";

                // Disable start button during ping
                StartPingButton.IsEnabled = false;
                Mouse.OverrideCursor = Cursors.Wait;

                // Perform ping
                var result = await NetworkManager.PingHostAsync(host, 1000, pingCount);

                // Update summary results
                PingSuccessRateTextBlock.Text = $"{result.Successful}/{result.Successful + result.Failed} ({(result.Successful * 100.0 / pingCount):F1}%)";

                if (result.Successful > 0)
                {
                    PingTimesTextBlock.Text = $"{result.MinTime}/{result.AverageTime}/{result.MaxTime} ms";
                    PingStatusTextBlock.Text = result.Successful == pingCount ? "Success" : "Partial Success";
                    PingStatusTextBlock.Foreground = result.Successful == pingCount ? Brushes.Green : Brushes.Orange;
                }
                else
                {
                    PingTimesTextBlock.Text = "N/A";
                    PingStatusTextBlock.Text = "Failed";
                    PingStatusTextBlock.Foreground = Brushes.Red;
                }

                // Add individual ping results to the list
                for (int i = 0; i < result.PingResults.Count; i++)
                {
                    var pingResult = result.PingResults[i];
                    _pingResults.Add(new PingResultItemDisplay
                    {
                        Index = i + 1,
                        Status = pingResult.Status,
                        Time = pingResult.Status == "Success" ? pingResult.Time : 0,
                        TTL = pingResult.TTL
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error pinging host: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);

                PingSuccessRateTextBlock.Text = "Error";
                PingTimesTextBlock.Text = "Error";
                PingStatusTextBlock.Text = "Error";
                PingStatusTextBlock.Foreground = Brushes.Red;
            }
            finally
            {
                StartPingButton.IsEnabled = true;
                Mouse.OverrideCursor = null;
            }
        }

        #endregion

        #region Task Scheduler Tab

        private void LoadScheduledTasks()
        {
            // In a real application, we would load actual scheduled tasks
            // For this demo, we'll just add some sample tasks
            _scheduledTasks.Clear();

            _scheduledTasks.Add(new ScheduledTask
            {
                Name = "Daily Backup",
                Schedule = "Daily at 10:00 PM",
                Action = "Run backup script",
                Status = "Enabled"
            });

            _scheduledTasks.Add(new ScheduledTask
            {
                Name = "Weekly System Scan",
                Schedule = "Sunday at 2:00 AM",
                Action = "Run system scan",
                Status = "Enabled"
            });

            _scheduledTasks.Add(new ScheduledTask
            {
                Name = "Monthly Report",
                Schedule = "1st day of month at 8:00 AM",
                Action = "Generate monthly report",
                Status = "Disabled"
            });
        }

        private void AddTaskButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Task creation is not implemented in this demo.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshTasksButton_Click(object sender, RoutedEventArgs e)
        {
            LoadScheduledTasks();
        }

        private void EditTaskButton_Click(object sender, RoutedEventArgs e)
        {
            if (ScheduledTasksListView.SelectedItem != null)
            {
                MessageBox.Show("Task editing is not implemented in this demo.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteTaskButton_Click(object sender, RoutedEventArgs e)
        {
            if (ScheduledTasksListView.SelectedItem is ScheduledTask selectedTask)
            {
                if (MessageBox.Show($"Are you sure you want to delete the task '{selectedTask.Name}'?",
                                   "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.Yes)
                {
                    _scheduledTasks.Remove(selectedTask);
                }
            }
        }

        private void RunTaskNowButton_Click(object sender, RoutedEventArgs e)
        {
            if (ScheduledTasksListView.SelectedItem is ScheduledTask selectedTask)
            {
                MessageBox.Show($"Task '{selectedTask.Name}' would be executed now.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        #endregion
    }

    #region Helper Classes

    public class ProcessInfo
    {
        public int Id { get; set; }
        public string ProcessName { get; set; }
        public string MainWindowTitle { get; set; }
        public double MemoryMB { get; set; }
    }

    public class MacroAction
    {
        public string ActionType { get; set; }
        public string Details { get; set; }
        public string Timestamp { get; set; }
    }

    public class ScheduledTask
    {
        public string Name { get; set; }
        public string Schedule { get; set; }
        public string Action { get; set; }
        public string Status { get; set; }
    }

    public class PingResultItemDisplay
    {
        public int Index { get; set; }
        public string Status { get; set; }
        public long Time { get; set; }
        public int TTL { get; set; }
    }

    #endregion
}
