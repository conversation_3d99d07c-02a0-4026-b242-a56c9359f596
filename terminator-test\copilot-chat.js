// <PERSON><PERSON><PERSON> to open Windows Copilot and start chatting
const fetch = require('node-fetch');

// Function to make a request to the Terminator server
async function makeRequest(endpoint, data = {}) {
  try {
    const response = await fetch(`http://localhost:9375${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error);
    throw error;
  }
}

// Sleep function for waiting
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Open Windows Copilot using keyboard shortcut (Win+C)
async function openCopilot() {
  console.log('Opening Windows Copilot using Win+C shortcut...');
  try {
    // Press Win+C to open Copilot
    const result = await makeRequest('/press_key', {
      key: 'Meta+c'
    });
    
    console.log('Keyboard shortcut result:', result);
    return result;
  } catch (error) {
    console.error('Error pressing keyboard shortcut:', error);
    
    // Try alternative method - using search
    console.log('Trying alternative method - using search...');
    try {
      // Press Win key to open search
      await makeRequest('/press_key', { key: 'Meta' });
      await sleep(1000);
      
      // Type "Copilot"
      await makeRequest('/type_text', { text: 'Copilot' });
      await sleep(1000);
      
      // Press Enter
      await makeRequest('/press_key', { key: 'Enter' });
      
      return { message: 'Attempted to open Copilot via search' };
    } catch (searchError) {
      console.error('Error with alternative method:', searchError);
      return null;
    }
  }
}

// Find Copilot window
async function findCopilotWindow() {
  console.log('Finding Copilot window...');
  try {
    // Wait a moment for Copilot to open
    await sleep(3000);
    
    // Try different possible window titles
    const possibleTitles = ['Copilot', 'Windows Copilot', 'Microsoft Copilot'];
    
    for (const title of possibleTitles) {
      console.log(`Searching for window with title containing "${title}"...`);
      const result = await makeRequest('/find_window', {
        titleContains: title,
        timeout_ms: 3000
      });
      
      if (result && !result.error) {
        console.log(`Found window with title containing "${title}":`, result);
        return result;
      }
    }
    
    console.error('Could not find Copilot window');
    return null;
  } catch (error) {
    console.error('Error finding Copilot window:', error);
    return null;
  }
}

// Capture screen to see what's visible
async function captureScreen() {
  console.log('Capturing screen...');
  try {
    const result = await makeRequest('/capture_screen');
    
    if (!result || result.error) {
      console.error('Could not capture screen');
      return null;
    }
    
    console.log('Screen capture OCR text (first 150 chars):', result.text.substring(0, 150) + '...');
    return result;
  } catch (error) {
    console.error('Error capturing screen:', error);
    return null;
  }
}

// Type a message in Copilot
async function typeMessageInCopilot(windowSelector, message) {
  console.log(`Typing message in Copilot: "${message}"...`);
  try {
    // First, try to find the input field
    // Since we don't know the exact selector, we'll try clicking near the bottom of the window
    // and then typing
    
    // Click in the chat input area (likely at the bottom of the window)
    const clickResult = await makeRequest('/click', {
      selector_chain: [windowSelector],
      timeout_ms: 5000
    });
    
    console.log('Click result:', clickResult);
    
    // Wait a moment
    await sleep(1000);
    
    // Type the message
    const typeResult = await makeRequest('/type_text', {
      text: message,
      timeout_ms: 5000
    });
    
    console.log('Type result:', typeResult);
    
    // Press Enter to send the message
    await sleep(500);
    const enterResult = await makeRequest('/press_key', {
      key: 'Enter'
    });
    
    console.log('Enter key result:', enterResult);
    
    return typeResult;
  } catch (error) {
    console.error('Error typing message in Copilot:', error);
    return null;
  }
}

// Main function
async function main() {
  try {
    // Open Windows Copilot
    await openCopilot();
    
    // Find Copilot window
    const copilotWindow = await findCopilotWindow();
    
    // If we couldn't find the Copilot window, capture the screen to see what's visible
    if (!copilotWindow) {
      console.log('Could not find Copilot window. Capturing screen to see what\'s visible...');
      await captureScreen();
      
      // Try again after a longer wait
      console.log('Waiting longer and trying again...');
      await sleep(5000);
      const secondAttempt = await findCopilotWindow();
      
      if (!secondAttempt) {
        console.log('Still could not find Copilot window. Capturing screen again...');
        await captureScreen();
        return;
      }
    }
    
    // Get the selector for the Copilot window
    const windowSelector = copilotWindow.suggested_selector || 'window:"Copilot"';
    
    // Capture screen to see what's visible
    await captureScreen();
    
    // Type a message in Copilot
    await typeMessageInCopilot(windowSelector, 'Hello Copilot! This message was sent automatically using the Terminator MCP agent.');
    
    // Capture screen again to see the result
    await sleep(2000);
    await captureScreen();
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
main();
