#!/usr/bin/env python3
"""
Simple Terminal-based AI Chat Client using OpenRouter API
"""

import os
import json
import requests
import argparse

def get_api_key():
    """Get the OpenRouter API key from environment variable or prompt user."""
    api_key = os.environ.get("OPENROUTER_API_KEY")
    if not api_key:
        print("OpenRouter API key not found in environment variables.")
        api_key = input("Please enter your OpenRouter API key: ")
        # Save to environment for this session
        os.environ["OPENROUTER_API_KEY"] = api_key
    return api_key

def send_message(messages, model="anthropic/claude-3-haiku", api_key=None):
    """Send a message to the AI model and return the response."""
    if not api_key:
        api_key = get_api_key()
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
        "HTTP-Referer": "https://terminal-chat.local",
        "X-Title": "Simple Terminal AI Chat"
    }
    
    data = {
        "model": model,
        "messages": messages,
        "stream": False
    }
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    response_data = response.json()
    return response_data['choices'][0]['message']['content']

def main():
    """Main function to run the chat client."""
    parser = argparse.ArgumentParser(description="Simple Terminal-based AI Chat Client")
    parser.add_argument("--model", type=str, default="anthropic/claude-3-haiku", help="Model to use")
    parser.add_argument("--system", type=str, help="System message to set the behavior of the assistant")
    
    args = parser.parse_args()
    
    print("========================================")
    print("       Simple Terminal AI Chat         ")
    print("========================================")
    print(f"Using model: {args.model}")
    print("Type 'exit' or 'quit' to end the conversation")
    print("========================================\n")
    
    # Initialize conversation
    conversation = []
    
    # Add system message if provided
    if args.system:
        system_message = {"role": "system", "content": args.system}
        conversation.append(system_message)
        print(f"System: {args.system}")
    
    # Main chat loop
    while True:
        try:
            # Get user input
            user_input = input("You: ")
            
            # Check for exit command
            if user_input.lower() in ['exit', 'quit']:
                break
            
            # Add user message to conversation
            user_message = {"role": "user", "content": user_input}
            conversation.append(user_message)
            
            print("AI is thinking...")
            
            # Get AI response
            ai_response = send_message(conversation, model=args.model)
            
            # Add AI response to conversation
            ai_message = {"role": "assistant", "content": ai_response}
            conversation.append(ai_message)
            
            # Print the response
            print(f"AI: {ai_response}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
