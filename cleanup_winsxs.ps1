# WinSxS and System Cleanup Script
# This script must be run as Administrator

Write-Host "=== Windows System Cleanup Script ===" -ForegroundColor Green
Write-Host "Checking administrator privileges..." -ForegroundColor Yellow

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Administrator privileges confirmed." -ForegroundColor Green

# Function to get disk space
function Get-DiskSpace {
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
    $freeGB = [math]::Round($disk.FreeSpace/1GB, 2)
    $usedGB = [math]::Round(($disk.Size - $disk.FreeSpace)/1GB, 2)
    $totalGB = [math]::Round($disk.Size/1GB, 2)
    Write-Host "C: Drive - Total: $totalGB GB, Used: $usedGB GB, Free: $freeGB GB" -ForegroundColor Cyan
    return $freeGB
}

Write-Host "`n=== Initial Disk Space ===" -ForegroundColor Yellow
$initialFree = Get-DiskSpace

Write-Host "`n=== Step 1: Analyzing Component Store ===" -ForegroundColor Yellow
try {
    dism /online /cleanup-image /analyzecomponentstore
} catch {
    Write-Host "Error analyzing component store: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Step 2: Component Store Cleanup ===" -ForegroundColor Yellow
try {
    Write-Host "Starting component cleanup (this may take several minutes)..." -ForegroundColor Yellow
    dism /online /cleanup-image /startcomponentcleanup
    Write-Host "Component cleanup completed." -ForegroundColor Green
} catch {
    Write-Host "Error during component cleanup: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Step 3: Windows Update Cleanup ===" -ForegroundColor Yellow
try {
    Write-Host "Cleaning superseded Windows updates..." -ForegroundColor Yellow
    dism /online /cleanup-image /spsuperseded
    Write-Host "Windows update cleanup completed." -ForegroundColor Green
} catch {
    Write-Host "Error during update cleanup: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Step 4: Reset Base (More Aggressive Cleanup) ===" -ForegroundColor Yellow
$response = Read-Host "Do you want to run ResetBase cleanup? This removes ability to uninstall recent updates (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    try {
        Write-Host "Running ResetBase cleanup (this may take a long time)..." -ForegroundColor Yellow
        dism /online /cleanup-image /startcomponentcleanup /resetbase
        Write-Host "ResetBase cleanup completed." -ForegroundColor Green
    } catch {
        Write-Host "Error during ResetBase cleanup: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Skipping ResetBase cleanup." -ForegroundColor Yellow
}

Write-Host "`n=== Step 5: Additional System Cleanup ===" -ForegroundColor Yellow
try {
    # Clean Windows Update cache
    Write-Host "Stopping Windows Update service..." -ForegroundColor Yellow
    Stop-Service -Name wuauserv -Force -ErrorAction SilentlyContinue
    
    Write-Host "Cleaning Windows Update cache..." -ForegroundColor Yellow
    Remove-Item "C:\Windows\SoftwareDistribution\Download\*" -Recurse -Force -ErrorAction SilentlyContinue
    
    Write-Host "Starting Windows Update service..." -ForegroundColor Yellow
    Start-Service -Name wuauserv -ErrorAction SilentlyContinue
    
    # Clean system temp files
    Write-Host "Cleaning system temp files..." -ForegroundColor Yellow
    Remove-Item "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue
    
    Write-Host "Additional cleanup completed." -ForegroundColor Green
} catch {
    Write-Host "Error during additional cleanup: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Final Disk Space ===" -ForegroundColor Yellow
$finalFree = Get-DiskSpace
$spaceFreed = $finalFree - $initialFree

Write-Host "`n=== Cleanup Summary ===" -ForegroundColor Green
Write-Host "Space freed: $([math]::Round($spaceFreed, 2)) GB" -ForegroundColor Green

if ($spaceFreed -gt 0) {
    Write-Host "Cleanup successful! You have freed up $([math]::Round($spaceFreed, 2)) GB of space." -ForegroundColor Green
} else {
    Write-Host "Cleanup completed, but no significant space was freed." -ForegroundColor Yellow
}

Write-Host "`nCleanup process completed!" -ForegroundColor Green
Read-Host "Press Enter to exit"
