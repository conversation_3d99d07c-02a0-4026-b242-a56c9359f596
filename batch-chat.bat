@echo off
setlocal enabledelayedexpansion

:: Simple AI Chat Simulator in Batch
title AI Chat Simulator

:: Clear screen and show welcome message
cls
echo ========================================
echo        Simple AI Chat Simulator
echo ========================================
echo This is a simple simulation of an AI chat about artificial intelligence topics.
echo Type 'exit' or 'quit' to end the conversation
echo Try asking about: 'what is AI', 'types of AI', 'machine learning', 'ethics',
echo                   'future of AI', 'risks', 'benefits', or 'AGI'
echo ========================================
echo.

:: Initial greeting
echo AI: Hello! I'm a simulated AI assistant focused on AI topics. How can I help you today?
echo.

:chat_loop
:: Get user input
set /p user_input=You: 

:: Check for exit command
if /i "%user_input%"=="exit" goto :end_chat
if /i "%user_input%"=="quit" goto :end_chat

:: Process the input and get a response
call :get_response "%user_input%"
echo.
echo AI: %response%
echo.

:: Continue the loop
goto :chat_loop

:end_chat
echo.
echo AI: Goodbye! Thanks for chatting.
pause
exit /b

:get_response
:: Convert input to lowercase for matching
set "query=%~1"
set "query=!query:A=a!"
set "query=!query:B=b!"
set "query=!query:C=c!"
set "query=!query:D=d!"
set "query=!query:E=e!"
set "query=!query:F=f!"
set "query=!query:G=g!"
set "query=!query:H=h!"
set "query=!query:I=i!"
set "query=!query:J=j!"
set "query=!query:K=k!"
set "query=!query:L=l!"
set "query=!query:M=m!"
set "query=!query:N=n!"
set "query=!query:O=o!"
set "query=!query:P=p!"
set "query=!query:Q=q!"
set "query=!query:R=r!"
set "query=!query:S=s!"
set "query=!query:T=t!"
set "query=!query:U=u!"
set "query=!query:V=v!"
set "query=!query:W=w!"
set "query=!query:X=x!"
set "query=!query:Y=y!"
set "query=!query:Z=z!"

:: Check for keywords and set response
if "!query!"=="hello" (
    set "response=Hello! I'm a simulated AI assistant. How can I help you with AI-related topics today?"
    exit /b
)

if "!query!"=="what is ai" (
    set "response=Artificial Intelligence (AI) refers to systems designed to perform tasks that typically require human intelligence. These include learning, reasoning, problem-solving, perception, and language understanding."
    exit /b
)

if "!query!"=="types of ai" (
    set "response=AI can be categorized as: 1) Narrow/Weak AI (designed for specific tasks), 2) General/Strong AI (hypothetical human-like intelligence), 3) Based on techniques (rule-based, machine learning, deep learning, etc.)"
    exit /b
)

if "!query!"=="machine learning" (
    set "response=Machine Learning is a subset of AI that enables systems to learn from data without explicit programming. Types include supervised learning, unsupervised learning, and reinforcement learning."
    exit /b
)

if "!query!"=="ethics" (
    set "response=AI ethics concerns include: bias and fairness, transparency, privacy, accountability, job displacement, safety, and autonomy. These require multidisciplinary approaches."
    exit /b
)

if "!query!"=="future of ai" (
    set "response=The future of AI may include more capable foundation models, multimodal AI, personalization, human-AI collaboration, specialized hardware, democratization, regulation, and scientific breakthroughs."
    exit /b
)

if "!query!"=="risks" (
    set "response=AI risks include bias, privacy concerns, security vulnerabilities, job displacement, power concentration, autonomous weapons, misinformation, overreliance, and long-term control challenges."
    exit /b
)

if "!query!"=="benefits" (
    set "response=AI benefits include advances in healthcare, climate solutions, productivity, accessibility, education, scientific discovery, safety improvements, creative tools, and economic growth."
    exit /b
)

if "!query!"=="agi" (
    set "response=Artificial General Intelligence (AGI) refers to hypothetical AI with human-like general intelligence across diverse domains. We don't have AGI today, and there's no consensus on when it might be achieved."
    exit /b
)

:: Check for partial matches
if "!query!"=="what is artificial intelligence" (
    set "response=Artificial Intelligence (AI) refers to systems designed to perform tasks that typically require human intelligence. These include learning, reasoning, problem-solving, perception, and language understanding."
    exit /b
)

:: Default response if no match
set "response=I'm a simulated AI assistant with limited responses. I can discuss basic AI concepts like 'what is AI', 'types of AI', 'machine learning', 'ethics', 'future of AI', 'risks', 'benefits', and 'AGI'. Please try asking about one of these topics."
exit /b
