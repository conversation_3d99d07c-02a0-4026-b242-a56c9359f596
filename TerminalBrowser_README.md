# Terminal Web Browser for Windows

A PowerShell-based terminal web browser that allows you to browse the internet directly from the command line without using a traditional web browser.

## Features

- Visit websites and view their content in text format
- Search the web using Google, Bing, or DuckDuckGo
- Download files from the internet
- Save and manage bookmarks
- Track browsing history
- Extract and follow links from web pages

## Requirements

- Windows with PowerShell 5.1 or later

## Installation

1. Download the `TerminalWebBrowser.ps1` and `TerminalBrowser.bat` files
2. Place them in a folder of your choice
3. Double-click on `TerminalBrowser.bat` to run the application

## Usage

### Main Menu

The main menu provides the following options:

1. **Visit a website**: Enter a URL to navigate to a specific website
2. **Search the web**: Search the internet using your preferred search engine
3. **View bookmarks**: See your saved bookmarks and visit them
4. **View history**: See your browsing history and revisit pages
5. **Download a file**: Download files from the internet
6. **Exit**: Close the application

### Viewing Websites

When viewing a website, you have several options:

- See a formatted text version of the page
- View the raw HTML
- Extract only the text content
- List all links on the page

### Command-Line Functions

You can also use the individual functions directly from PowerShell:

```powershell
# Import the module
. .\TerminalWebBrowser.ps1

# Visit a website
Show-WebPage -Url "https://example.com"

# View only text content
Show-WebPage -Url "https://example.com" -TextOnly

# View only links
Show-WebPage -Url "https://example.com" -Links

# Search the web
Search-Web -Query "terminal web browsing" -Engine "google"

# Download a file
Save-WebFile -Url "https://example.com/file.pdf" -OutputPath "C:\Downloads\file.pdf"

# Add a bookmark
Add-Bookmark -Url "https://example.com" -Title "Example Website" -Folder "Favorites"
```

## Advanced Features

### Custom User Agent

You can modify the user agent string to identify as different browsers:

```powershell
$global:userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
```

### Default Download Path

Change where downloaded files are saved by default:

```powershell
$global:defaultDownloadPath = "C:\Your\Custom\Path"
```

## Limitations

- No JavaScript support
- Limited rendering of complex web pages
- No support for cookies or sessions
- No multimedia content (images, videos, etc.)

## Privacy and Security

- All browsing history is stored locally on your computer
- No data is sent to any servers other than the websites you visit
- Downloads should be scanned with antivirus software before opening

## License

This script is provided as-is with no warranty. Use at your own risk.
