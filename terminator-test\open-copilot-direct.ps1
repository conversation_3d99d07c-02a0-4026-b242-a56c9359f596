# PowerShell script to open Windows Copilot using Windows API

# Add reference to Windows Forms for SendKeys
Add-Type -AssemblyName System.Windows.Forms

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class KeyboardSend
{
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

    public const int KEYEVENTF_EXTENDEDKEY = 0x0001;
    public const int KEYEVENTF_KEYUP = 0x0002;

    public const byte VK_LWIN = 0x5B;  // Left Windows key
    public const byte VK_C = 0x43;     // C key
    public const byte VK_RETURN = 0x0D; // Enter key

    public static void SendWinC()
    {
        // Press Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);

        // Press C key
        keybd_event(VK_C, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);

        // Release C key
        keybd_event(VK_C, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);

        // Release Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }

    public static void SendEnter()
    {
        // Press Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);

        // Release Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
}
"@

Write-Host "Attempting to open Windows Copilot using Win+C shortcut..."

# Send Win+C to open Copilot
[KeyboardSend]::SendWinC()

# Wait for Copilot to open
Write-Host "Waiting for Copilot to open..."
Start-Sleep -Seconds 3

# Type a message
Write-Host "Typing a message..."
$message = "Hello Copilot! This message was sent automatically using PowerShell."
[System.Windows.Forms.SendKeys]::SendWait($message)

# Send Enter to submit the message
Start-Sleep -Milliseconds 500
[KeyboardSend]::SendEnter()

Write-Host "Message sent!"
