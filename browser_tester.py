#!/usr/bin/env python3
"""
PrivateFox Browser Tester
This script tests the PrivateFox browser with various websites.
"""

import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QUrl, QTimer
from private_browser import PrivateBrowser

class BrowserTester:
    """Class to test the PrivateFox browser with various websites"""
    
    def __init__(self):
        """Initialize the browser tester"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("PrivateFox Tester")
        
        # Create browser instance
        print("Creating browser instance...")
        self.browser = PrivateBrowser()
        
        # Set up test websites
        self.test_sites = [
            ("DuckDuckGo", "https://duckduckgo.com"),
            ("YouTube", "https://www.youtube.com"),
            ("Google", "https://www.google.com"),
            ("Wikipedia", "https://www.wikipedia.org"),
            ("GitHub", "https://github.com"),
            ("Reddit", "https://www.reddit.com"),
            ("Twitter", "https://twitter.com"),
            ("Facebook", "https://www.facebook.com"),
            ("Amazon", "https://www.amazon.com"),
            ("Netflix", "https://www.netflix.com")
        ]
        
        # Set up timer for testing
        self.current_site_index = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.test_next_site)
        
    def start_testing(self):
        """Start the browser testing"""
        print("Starting browser testing...")
        self.browser.show()
        
        # Start the timer to test sites
        self.timer.start(10000)  # Test each site for 10 seconds
        
        # Start the first test
        self.test_next_site()
        
        # Run the application
        sys.exit(self.app.exec())
        
    def test_next_site(self):
        """Test the next website in the list"""
        if self.current_site_index < len(self.test_sites):
            site_name, site_url = self.test_sites[self.current_site_index]
            print(f"Testing {site_name} ({site_url})...")
            
            # Navigate to the site
            current_tab = self.browser.tabs.currentWidget()
            current_tab.load(QUrl(site_url))
            
            # Increment the index
            self.current_site_index += 1
        else:
            print("Testing completed!")
            self.timer.stop()

if __name__ == "__main__":
    tester = BrowserTester()
    tester.start_testing()
