{"name": "ai-explorer", "version": "0.1.0", "description": "AI-powered UI automation code generator using Vercel AI SDK and Terminator", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts"}, "keywords": ["ai", "ui automation", "terminator", "vercel ai sdk", "gemini"], "author": "", "license": "ISC", "dependencies": {"ai": "^4.3.5", "@google/generative-ai": "^0.14.0", "@ai-sdk/google": "^1.2.10", "dotenv": "^16.4.5", "zod": "^3.23.8", "inquirer": "^9.2.23", "node-fetch": "^3.3.2", "desktop-use": "file:../../ts-sdk"}, "devDependencies": {"@types/node": "^20.14.2", "@types/inquirer": "^9.0.7", "tsx": "^4.15.6", "typescript": "^5.4.5"}}