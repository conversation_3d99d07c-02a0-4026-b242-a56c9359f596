# TerminalTools.ps1
# A unified launcher for terminal-based tools
# Created by Augment Agent

#Requires -Version 5.1

# Show the main menu
function Show-MainMenu {
    Clear-Host
    Write-Host "Terminal Tools Launcher" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor DarkGray

    Write-Host "`nAvailable Tools:" -ForegroundColor Cyan
    Write-Host "1. AI Chat Terminal (OpenRouter)" -ForegroundColor White
    Write-Host "2. Terminal Web Browser" -ForegroundColor White
    Write-Host "3. Exit" -ForegroundColor White

    $choice = Read-Host "`nEnter your choice (1-3)"

    switch ($choice) {
        "1" {
            # Launch AI Chat Terminal
            $scriptPath = Join-Path $PSScriptRoot "AITerminal.ps1"
            if (Test-Path $scriptPath) {
                & $scriptPath
            } else {
                Write-Host "Error: AITerminal.ps1 not found in the current directory." -ForegroundColor Red
                Read-Host "Press Enter to continue"
            }
            Show-MainMenu
        }
        "2" {
            # Launch Terminal Web Browser
            $scriptPath = Join-Path $PSScriptRoot "FixedTerminalWebBrowser.ps1"
            if (Test-Path $scriptPath) {
                & $scriptPath
            } else {
                Write-Host "Error: FixedTerminalWebBrowser.ps1 not found in the current directory." -ForegroundColor Red
                Read-Host "Press Enter to continue"
            }
            Show-MainMenu
        }
        "3" {
            Write-Host "Exiting Terminal Tools. Goodbye!" -ForegroundColor Cyan
            return
        }
        default {
            Write-Host "Invalid choice. Please try again." -ForegroundColor Red
            Read-Host "Press Enter to continue"
            Show-MainMenu
        }
    }
}

# Start the application
Show-MainMenu
