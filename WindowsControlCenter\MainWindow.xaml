<Window x:Class="WindowsControlCenter.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Windows Control Center" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2c3e50" Padding="10">
            <TextBlock Text="Windows Control Center" FontSize="24" Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="5">
            <TabItem Header="Running Applications">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,5">
                        <Button x:Name="RefreshAppsButton" Content="Refresh" Width="100" Margin="5" Click="RefreshAppsButton_Click"/>
                        <TextBlock Text="Filter:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                        <TextBox x:Name="FilterTextBox" Width="200" Margin="0,5" TextChanged="FilterTextBox_TextChanged"/>
                    </StackPanel>

                    <ListView x:Name="ApplicationsListView" Grid.Row="1" Margin="5" SelectionChanged="ApplicationsListView_SelectionChanged">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Process ID" DisplayMemberBinding="{Binding Id}" Width="80"/>
                                <GridViewColumn Header="Name" DisplayMemberBinding="{Binding ProcessName}" Width="150"/>
                                <GridViewColumn Header="Window Title" DisplayMemberBinding="{Binding MainWindowTitle}" Width="300"/>
                                <GridViewColumn Header="Memory (MB)" DisplayMemberBinding="{Binding MemoryMB}" Width="100"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="5">
                        <Button x:Name="BringToFrontButton" Content="Bring to Front" Width="100" Margin="5" Click="BringToFrontButton_Click"/>
                        <Button x:Name="MinimizeButton" Content="Minimize" Width="100" Margin="5" Click="MinimizeButton_Click"/>
                        <Button x:Name="MaximizeButton" Content="Maximize" Width="100" Margin="5" Click="MaximizeButton_Click"/>
                        <Button x:Name="CloseAppButton" Content="Close App" Width="100" Margin="5" Click="CloseAppButton_Click" Background="#e74c3c" Foreground="White"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem Header="System Information">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button x:Name="RefreshSystemInfoButton" Content="Refresh" Width="100" Margin="5" Click="RefreshSystemInfoButton_Click"/>
                    </StackPanel>

                    <ScrollViewer Grid.Row="1" Margin="5">
                        <StackPanel>
                            <GroupBox Header="CPU Information" Margin="5">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CpuNameTextBlock" Text="Loading..." Margin="5"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Cores:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="CpuCoresTextBlock" Text="Loading..." Margin="5"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Usage:" Margin="5" FontWeight="Bold"/>
                                    <ProgressBar Grid.Row="2" Grid.Column="1" x:Name="CpuUsageProgressBar" Height="20" Margin="5" Maximum="100"/>
                                </Grid>
                            </GroupBox>

                            <GroupBox Header="Memory Information" Margin="5">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Total:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="MemoryTotalTextBlock" Text="Loading..." Margin="5"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Available:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="MemoryAvailableTextBlock" Text="Loading..." Margin="5"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Usage:" Margin="5" FontWeight="Bold"/>
                                    <ProgressBar Grid.Row="2" Grid.Column="1" x:Name="MemoryUsageProgressBar" Height="20" Margin="5" Maximum="100"/>
                                </Grid>
                            </GroupBox>

                            <GroupBox Header="Operating System Information" Margin="5">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="OsNameTextBlock" Text="Loading..." Margin="5"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Version:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="OsVersionTextBlock" Text="Loading..." Margin="5"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Uptime:" Margin="5" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="OsUptimeTextBlock" Text="Loading..." Margin="5"/>
                                </Grid>
                            </GroupBox>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </TabItem>

            <!-- New Services Tab -->
            <TabItem Header="Services">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <Button x:Name="RefreshServicesButton" Content="Refresh" Width="100" Margin="5" Click="RefreshServicesButton_Click"/>
                            <ComboBox x:Name="ServiceFilterComboBox" Width="150" Margin="5" SelectionChanged="ServiceFilterComboBox_SelectionChanged">
                                <ComboBoxItem Content="All Services" IsSelected="True"/>
                                <ComboBoxItem Content="Running Only"/>
                                <ComboBoxItem Content="Stopped Only"/>
                                <ComboBoxItem Content="Automatic Startup"/>
                                <ComboBoxItem Content="Manual Startup"/>
                                <ComboBoxItem Content="Disabled"/>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                            <TextBlock Text="Search:" VerticalAlignment="Center" Margin="5"/>
                            <TextBox x:Name="ServiceSearchTextBox" Width="200" Margin="5" TextChanged="ServiceSearchTextBox_TextChanged"/>
                        </StackPanel>

                        <TextBlock Grid.Column="2" x:Name="AdminWarningTextBlock" Text="Administrator privileges required for full control"
                                   Foreground="Red" VerticalAlignment="Center" Margin="10,0" Visibility="Collapsed"/>
                    </Grid>

                    <ListView x:Name="ServicesListView" Grid.Row="1" Margin="5" SelectionChanged="ServicesListView_SelectionChanged">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Name" DisplayMemberBinding="{Binding DisplayName}" Width="250"/>
                                <GridViewColumn Header="Status" DisplayMemberBinding="{Binding Status}" Width="100"/>
                                <GridViewColumn Header="Startup Type" DisplayMemberBinding="{Binding StartupType}" Width="120"/>
                                <GridViewColumn Header="Description" DisplayMemberBinding="{Binding Description}" Width="300"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <Grid Grid.Row="2" Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <Button x:Name="StartServiceButton" Content="Start" Width="80" Margin="5" Click="StartServiceButton_Click" IsEnabled="False"/>
                            <Button x:Name="StopServiceButton" Content="Stop" Width="80" Margin="5" Click="StopServiceButton_Click" IsEnabled="False"/>
                            <Button x:Name="RestartServiceButton" Content="Restart" Width="80" Margin="5" Click="RestartServiceButton_Click" IsEnabled="False"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="Set Startup Type:" VerticalAlignment="Center" Margin="5"/>
                            <ComboBox x:Name="StartupTypeComboBox" Width="120" Margin="5" SelectionChanged="StartupTypeComboBox_SelectionChanged" IsEnabled="False">
                                <ComboBoxItem Content="Automatic"/>
                                <ComboBoxItem Content="Manual"/>
                                <ComboBoxItem Content="Disabled"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                </Grid>
            </TabItem>

            <TabItem Header="Automation">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button x:Name="StartRecordingButton" Content="Start Recording" Width="120" Margin="5" Click="StartRecordingButton_Click"/>
                        <Button x:Name="StopRecordingButton" Content="Stop Recording" Width="120" Margin="5" Click="StopRecordingButton_Click" IsEnabled="False"/>
                        <Button x:Name="PlayMacroButton" Content="Play Macro" Width="120" Margin="5" Click="PlayMacroButton_Click" IsEnabled="False"/>
                    </StackPanel>

                    <ListView x:Name="MacroActionsListView" Grid.Row="1" Margin="5">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Action Type" DisplayMemberBinding="{Binding ActionType}" Width="100"/>
                                <GridViewColumn Header="Details" DisplayMemberBinding="{Binding Details}" Width="400"/>
                                <GridViewColumn Header="Time" DisplayMemberBinding="{Binding Timestamp}" Width="150"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="5">
                        <Button x:Name="SaveMacroButton" Content="Save Macro" Width="100" Margin="5" Click="SaveMacroButton_Click" IsEnabled="False"/>
                        <Button x:Name="LoadMacroButton" Content="Load Macro" Width="100" Margin="5" Click="LoadMacroButton_Click"/>
                        <Button x:Name="ClearMacroButton" Content="Clear" Width="100" Margin="5" Click="ClearMacroButton_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Network Tab -->
            <TabItem Header="Network">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TabControl Grid.Row="1">
                        <!-- Network Adapters Tab -->
                        <TabItem Header="Adapters">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                                    <Button x:Name="RefreshAdaptersButton" Content="Refresh" Width="100" Margin="5" Click="RefreshAdaptersButton_Click"/>
                                    <TextBlock Text="Administrator privileges required for some operations" Foreground="Red"
                                               x:Name="NetworkAdminWarningTextBlock" VerticalAlignment="Center" Margin="10,0" Visibility="Collapsed"/>
                                </StackPanel>

                                <ListView x:Name="NetworkAdaptersListView" Grid.Row="1" Margin="5" SelectionChanged="NetworkAdaptersListView_SelectionChanged">
                                    <ListView.View>
                                        <GridView>
                                            <GridViewColumn Header="Name" DisplayMemberBinding="{Binding Name}" Width="150"/>
                                            <GridViewColumn Header="Status" DisplayMemberBinding="{Binding Status}" Width="80"/>
                                            <GridViewColumn Header="IP Address" DisplayMemberBinding="{Binding IPv4Address}" Width="120"/>
                                            <GridViewColumn Header="MAC Address" DisplayMemberBinding="{Binding MacAddress}" Width="150"/>
                                            <GridViewColumn Header="Type" DisplayMemberBinding="{Binding Type}" Width="100"/>
                                            <GridViewColumn Header="Speed" DisplayMemberBinding="{Binding Speed}" Width="100"/>
                                        </GridView>
                                    </ListView.View>
                                </ListView>

                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="5">
                                        <Button x:Name="EnableAdapterButton" Content="Enable" Width="80" Margin="5" Click="EnableAdapterButton_Click" IsEnabled="False"/>
                                        <Button x:Name="DisableAdapterButton" Content="Disable" Width="80" Margin="5" Click="DisableAdapterButton_Click" IsEnabled="False"/>
                                        <Button x:Name="RenewDhcpButton" Content="Renew DHCP" Width="100" Margin="5" Click="RenewDhcpButton_Click" IsEnabled="False"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="5">
                                        <Button x:Name="AdapterDetailsButton" Content="Details" Width="80" Margin="5" Click="AdapterDetailsButton_Click" IsEnabled="False"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </TabItem>

                        <!-- Network Statistics Tab -->
                        <TabItem Header="Statistics">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                                    <Button x:Name="RefreshNetworkStatsButton" Content="Refresh" Width="100" Margin="5" Click="RefreshNetworkStatsButton_Click"/>
                                    <Button x:Name="FlushDnsButton" Content="Flush DNS" Width="100" Margin="5" Click="FlushDnsButton_Click"/>
                                </StackPanel>

                                <ScrollViewer Grid.Row="1" Margin="5">
                                    <StackPanel>
                                        <GroupBox Header="Network Traffic" Margin="5">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Bytes Sent:" Margin="5" FontWeight="Bold"/>
                                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="BytesSentTextBlock" Text="Loading..." Margin="5"/>

                                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Bytes Received:" Margin="5" FontWeight="Bold"/>
                                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="BytesReceivedTextBlock" Text="Loading..." Margin="5"/>

                                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Packets Sent:" Margin="5" FontWeight="Bold"/>
                                                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="PacketsSentTextBlock" Text="Loading..." Margin="5"/>

                                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Packets Received:" Margin="5" FontWeight="Bold"/>
                                                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="PacketsReceivedTextBlock" Text="Loading..." Margin="5"/>

                                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Errors:" Margin="5" FontWeight="Bold"/>
                                                <TextBlock Grid.Row="4" Grid.Column="1" x:Name="NetworkErrorsTextBlock" Text="Loading..." Margin="5"/>

                                                <TextBlock Grid.Row="5" Grid.Column="0" Text="Active Connections:" Margin="5" FontWeight="Bold"/>
                                                <TextBlock Grid.Row="5" Grid.Column="1" x:Name="ActiveConnectionsTextBlock" Text="Loading..." Margin="5"/>
                                            </Grid>
                                        </GroupBox>
                                    </StackPanel>
                                </ScrollViewer>
                            </Grid>
                        </TabItem>

                        <!-- Network Tools Tab -->
                        <TabItem Header="Tools">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <GroupBox Header="Ping Tool" Grid.Row="0" Margin="5">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="Host:" VerticalAlignment="Center" Margin="5"/>
                                        <TextBox Grid.Column="1" x:Name="PingHostTextBox" Margin="5" Text="*******"/>

                                        <TextBlock Grid.Column="2" Text="Count:" VerticalAlignment="Center" Margin="5"/>
                                        <ComboBox Grid.Column="3" x:Name="PingCountComboBox" Width="60" Margin="5" SelectedIndex="0">
                                            <ComboBoxItem Content="4"/>
                                            <ComboBoxItem Content="8"/>
                                            <ComboBoxItem Content="16"/>
                                            <ComboBoxItem Content="32"/>
                                            <ComboBoxItem Content="64"/>
                                        </ComboBox>

                                        <Button Grid.Column="4" x:Name="StartPingButton" Content="Start Ping" Width="100" Margin="5" Click="StartPingButton_Click"/>
                                    </Grid>
                                </GroupBox>

                                <Grid Grid.Row="1">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <GroupBox Header="Ping Results" Grid.Row="0" Margin="5">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Host:" Margin="5" FontWeight="Bold"/>
                                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="PingHostResultTextBlock" Text="-" Margin="5"/>

                                            <TextBlock Grid.Row="0" Grid.Column="2" Text="Success Rate:" Margin="5" FontWeight="Bold"/>
                                            <TextBlock Grid.Row="0" Grid.Column="3" x:Name="PingSuccessRateTextBlock" Text="-" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Min/Avg/Max:" Margin="5" FontWeight="Bold"/>
                                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="PingTimesTextBlock" Text="-" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="2" Text="Status:" Margin="5" FontWeight="Bold"/>
                                            <TextBlock Grid.Row="1" Grid.Column="3" x:Name="PingStatusTextBlock" Text="-" Margin="5"/>
                                        </Grid>
                                    </GroupBox>

                                    <ListView x:Name="PingResultsListView" Grid.Row="1" Margin="5">
                                        <ListView.View>
                                            <GridView>
                                                <GridViewColumn Header="#" DisplayMemberBinding="{Binding Index}" Width="40"/>
                                                <GridViewColumn Header="Status" DisplayMemberBinding="{Binding Status}" Width="100"/>
                                                <GridViewColumn Header="Time (ms)" DisplayMemberBinding="{Binding Time}" Width="100"/>
                                                <GridViewColumn Header="TTL" DisplayMemberBinding="{Binding TTL}" Width="60"/>
                                            </GridView>
                                        </ListView.View>
                                    </ListView>
                                </Grid>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </TabItem>

            <TabItem Header="Task Scheduler">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button x:Name="AddTaskButton" Content="Add Task" Width="100" Margin="5" Click="AddTaskButton_Click"/>
                        <Button x:Name="RefreshTasksButton" Content="Refresh" Width="100" Margin="5" Click="RefreshTasksButton_Click"/>
                    </StackPanel>

                    <ListView x:Name="ScheduledTasksListView" Grid.Row="1" Margin="5">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Task Name" DisplayMemberBinding="{Binding Name}" Width="150"/>
                                <GridViewColumn Header="Schedule" DisplayMemberBinding="{Binding Schedule}" Width="150"/>
                                <GridViewColumn Header="Action" DisplayMemberBinding="{Binding Action}" Width="200"/>
                                <GridViewColumn Header="Status" DisplayMemberBinding="{Binding Status}" Width="100"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="5">
                        <Button x:Name="EditTaskButton" Content="Edit Task" Width="100" Margin="5" Click="EditTaskButton_Click"/>
                        <Button x:Name="DeleteTaskButton" Content="Delete Task" Width="100" Margin="5" Click="DeleteTaskButton_Click"/>
                        <Button x:Name="RunTaskNowButton" Content="Run Now" Width="100" Margin="5" Click="RunTaskNowButton_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
