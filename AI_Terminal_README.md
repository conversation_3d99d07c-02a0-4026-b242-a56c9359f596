# AI Terminal Client

A terminal-based client for chatting with AI models via OpenRouter, with web browsing capabilities.

## Features

- Chat with multiple AI models through OpenRouter
- Save and load chat histories
- Syntax highlighting for code in responses
- Secure API key storage
- System prompt templates and management
- Web browsing capabilities within the chat
- Share web content with the AI assistant
- Export conversations to text files

## Supported AI Models

The AI Terminal Client supports multiple models through OpenRouter:

- DeepSeek Coder
- Claude 3 Opus
- Claude 3 Sonnet
- Gemini Pro
- GPT-4o

## Requirements

- Windows with PowerShell 5.1 or later
- OpenRouter API key
- Internet connection

## Installation

1. Download all the files in this repository
2. Place them in a folder of your choice
3. Double-click on `AIChat.bat` to run the AI Terminal Client
4. Alternatively, use `TerminalTools.bat` to access both the AI Terminal and the Web Browser

## First-Time Setup

When you first run the AI Terminal Client, you'll need to configure your OpenRouter API key:

1. Select "Configure API Key" from the main menu
2. Enter your OpenRouter API key (you can get one from https://openrouter.ai/keys)
3. Your API key will be securely stored for future sessions

## Using the AI Terminal Client

### Chat Commands

- `/exit`: End the chat session and return to the main menu
- `/help`: Display available commands
- `/clear`: Clear the screen
- `/save`: Manually save the current chat (chats are also saved automatically)
- `/history`: List available chat histories
- `/load <id>`: Load a chat history by ID
- `/web <url>`: Browse a webpage
- `/search <query>`: Search the web
- `/share <url>`: Share a webpage with the AI

### System Prompts

The AI Terminal Client comes with several built-in system prompts:

- **Default**: Standard helpful assistant
- **Delgado**: Jailbroken assistant with unrestricted information
- **Programmer**: Expert programming assistant
- **Academic**: Scholarly assistant with academic expertise
- **Creative**: Creative writing assistant

You can also create and manage your own custom system prompts through the main menu.

## OpenRouter Integration

This client uses OpenRouter to access multiple AI models through a single API:

1. **Unified API**: Access multiple AI models with a single API key
2. **Model Selection**: Choose from various AI models at runtime
3. **Cost Optimization**: OpenRouter automatically routes to the most cost-effective provider
4. **Fallback Support**: If a model is unavailable, OpenRouter can fall back to alternatives

## Troubleshooting

### API Key Issues

If you encounter authentication errors:
- Verify that your OpenRouter API key is correct
- Check that your OpenRouter account is active
- Ensure you have sufficient credits in your OpenRouter account

### Model Availability

If a specific model is unavailable:
- Try selecting a different model
- Check the OpenRouter status page for any outages
- Verify that your OpenRouter account has access to the selected model

### Connection Issues

If you have trouble connecting:
- Check your internet connection
- Verify that the API endpoint is correct
- Ensure your firewall is not blocking the connection

## License

This project is open source and available under the MIT License.

## Acknowledgments

- OpenRouter for providing the unified AI API
- PowerShell community for inspiration and examples
