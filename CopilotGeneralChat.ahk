#NoEnv  ; Recommended for performance and compatibility with future AutoHotkey releases.
#SingleInstance force  ; Ensures only one instance of this script is running
SendMode Input  ; Recommended for new scripts due to its superior speed and reliability.
SetWorkingDir %A_ScriptDir%  ; Ensures a consistent starting directory.

; Array of general chat messages to send to Copilot
global messages := []
messages.Push("Hello, how are you today?")
messages.Push("What's the weather like in New York City right now?")
messages.Push("Tell me an interesting fact about space.")
messages.Push("What's a good recipe for chocolate chip cookies?")
messages.Push("Can you recommend a good book to read?")

global currentMessageIndex := 1

; Function to activate Windows Copilot and send a message
SendToCopilot(message) {
    ; Try to activate Windows Copilot using different possible window identifiers
    WinActivate, ahk_exe Copilot.exe
    if !WinActive("ahk_exe Copilot.exe") {
        WinActivate, Windows Copilot
        if !WinActive("Windows Copilot") {
            ; Try to find it as a panel in Edge
            WinActivate, ahk_exe msedge.exe
            if !WinActive("ahk_exe msedge.exe") {
                MsgBox, Could not find Windows Copilot window. Please make sure it's open.
                return
            }
        }
    }
    
    ; Wait a bit for the window to be fully active
    Sleep, 500
    
    ; Clear any existing text (Ctrl+A then Delete)
    Send, ^a
    Sleep, 100
    Send, {Delete}
    Sleep, 100
    
    ; Send the message
    SendInput, %message%
    Sleep, 200
    Send, {Enter}
    
    ; Show a notification that the message was sent
    TrayTip, Message Sent, %message%, 2
}

; Send the next message in the sequence
SendNextMessage() {
    if (currentMessageIndex <= messages.MaxIndex()) {
        SendToCopilot(messages[currentMessageIndex])
        currentMessageIndex++
    } else {
        MsgBox, All messages have been sent!
    }
}

; Hotkey to send the next message (Ctrl+Alt+N)
^!n::
    SendNextMessage()
    return

; Hotkey to restart from the beginning (Ctrl+Alt+R)
^!r::
    currentMessageIndex := 1
    MsgBox, Reset to first message. Press Ctrl+Alt+N to start.
    return

; Custom message input (Ctrl+Alt+C)
^!c::
    InputBox, customMessage, Custom Message, Enter a custom message to send to Copilot:
    if (!ErrorLevel) ; If user didn't cancel
        SendToCopilot(customMessage)
    return

; Show help (Ctrl+Alt+H)
^!h::
    helpText := "Copilot General Chat Test`n`n"
    helpText .= "Ctrl+Alt+N: Send next predefined message`n"
    helpText .= "Ctrl+Alt+R: Reset to first message`n"
    helpText .= "Ctrl+Alt+C: Send custom message`n"
    helpText .= "Ctrl+Alt+H: Show this help`n`n"
    helpText .= "Current message index: " . currentMessageIndex . " of " . messages.MaxIndex()
    
    MsgBox, %helpText%
    return

; Show initial instructions
MsgBox, 0, Copilot General Chat Test, Script is ready!`n`nPress Ctrl+Alt+N to send the first message to Copilot.`nPress Ctrl+Alt+H for help.
