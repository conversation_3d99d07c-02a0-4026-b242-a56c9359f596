# PowerShell script to open Windows Copilot and start chatting

# Function to make a request to the Terminator server
function Invoke-TerminatorRequest {
    param (
        [string]$Endpoint,
        [hashtable]$Data = @{}
    )
    
    $url = "http://localhost:9375$Endpoint"
    $jsonData = $Data | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -ContentType "application/json" -Body $jsonData
        return $response
    }
    catch {
        Write-Error "Error making request to $Endpoint`: $_"
        return $null
    }
}

# Function to wait for a specified time
function Wait-Time {
    param (
        [int]$Milliseconds
    )
    
    Start-Sleep -Milliseconds $Milliseconds
}

# Step 1: Try to open Windows Copilot using keyboard shortcut
Write-Host "Attempting to open Windows Copilot..."

# Method 1: Try using keyboard shortcut Win+C
try {
    $keyResult = Invoke-TerminatorRequest -Endpoint "/press_keys" -Data @{
        keys = @("Meta", "c")
    }
    Write-Host "Keyboard shortcut result: $keyResult"
}
catch {
    Write-Host "Error using keyboard shortcut: $_"
    
    # Method 2: Try using search
    Write-Host "Trying alternative method - using search..."
    try {
        # Press Win key
        Invoke-TerminatorRequest -Endpoint "/press_keys" -Data @{
            keys = @("Meta")
        }
        Wait-Time 1000
        
        # Type "Copilot"
        Invoke-TerminatorRequest -Endpoint "/type" -Data @{
            text = "Copilot"
        }
        Wait-Time 1000
        
        # Press Enter
        Invoke-TerminatorRequest -Endpoint "/press_keys" -Data @{
            keys = @("Enter")
        }
    }
    catch {
        Write-Host "Error with alternative method: $_"
    }
}

# Wait for Copilot to open
Write-Host "Waiting for Copilot to open..."
Wait-Time 3000

# Step 2: Find Copilot window
Write-Host "Finding Copilot window..."
$copilotWindow = $null

# Try different possible window titles
$possibleTitles = @("Copilot", "Windows Copilot", "Microsoft Copilot")

foreach ($title in $possibleTitles) {
    Write-Host "Searching for window with title containing '$title'..."
    try {
        $result = Invoke-TerminatorRequest -Endpoint "/find_window" -Data @{
            titleContains = $title
            timeout_ms = 3000
        }
        
        if ($result -and -not $result.error) {
            Write-Host "Found window with title containing '$title'"
            $copilotWindow = $result
            break
        }
    }
    catch {
        Write-Host "Error finding window with title '$title': $_"
    }
}

if (-not $copilotWindow) {
    Write-Host "Could not find Copilot window. Capturing screen to see what's visible..."
    $screenCapture = Invoke-TerminatorRequest -Endpoint "/capture_screen" -Data @{}
    if ($screenCapture -and $screenCapture.text) {
        Write-Host "Screen capture OCR text (first 150 chars): $($screenCapture.text.Substring(0, [Math]::Min(150, $screenCapture.text.Length)))..."
    }
    
    # Try again after a longer wait
    Write-Host "Waiting longer and trying again..."
    Wait-Time 5000
    
    foreach ($title in $possibleTitles) {
        try {
            $result = Invoke-TerminatorRequest -Endpoint "/find_window" -Data @{
                titleContains = $title
                timeout_ms = 3000
            }
            
            if ($result -and -not $result.error) {
                Write-Host "Found window with title containing '$title'"
                $copilotWindow = $result
                break
            }
        }
        catch {
            Write-Host "Error finding window with title '$title': $_"
        }
    }
    
    if (-not $copilotWindow) {
        Write-Host "Still could not find Copilot window. Capturing screen again..."
        $screenCapture = Invoke-TerminatorRequest -Endpoint "/capture_screen" -Data @{}
        if ($screenCapture -and $screenCapture.text) {
            Write-Host "Screen capture OCR text (first 150 chars): $($screenCapture.text.Substring(0, [Math]::Min(150, $screenCapture.text.Length)))..."
        }
        exit
    }
}

# Step 3: Type a message in Copilot
$windowSelector = $copilotWindow.suggested_selector
if (-not $windowSelector) {
    $windowSelector = "window:`"$($possibleTitles[0])`""
}

Write-Host "Typing message in Copilot..."
try {
    # Click in the chat input area
    $clickResult = Invoke-TerminatorRequest -Endpoint "/click" -Data @{
        selector_chain = @($windowSelector)
        timeout_ms = 5000
    }
    Write-Host "Click result: $clickResult"
    
    # Wait a moment
    Wait-Time 1000
    
    # Type the message
    $message = "Hello Copilot! This message was sent automatically using the Terminator MCP agent."
    $typeResult = Invoke-TerminatorRequest -Endpoint "/type" -Data @{
        text = $message
        timeout_ms = 5000
    }
    Write-Host "Type result: $typeResult"
    
    # Press Enter to send the message
    Wait-Time 500
    $enterResult = Invoke-TerminatorRequest -Endpoint "/press_keys" -Data @{
        keys = @("Enter")
    }
    Write-Host "Enter key result: $enterResult"
}
catch {
    Write-Host "Error typing message in Copilot: $_"
}

# Capture screen again to see the result
Wait-Time 2000
$finalScreenCapture = Invoke-TerminatorRequest -Endpoint "/capture_screen" -Data @{}
if ($finalScreenCapture -and $finalScreenCapture.text) {
    Write-Host "Final screen capture OCR text (first 150 chars): $($finalScreenCapture.text.Substring(0, [Math]::Min(150, $finalScreenCapture.text.Length)))..."
}

Write-Host "Test completed!"
