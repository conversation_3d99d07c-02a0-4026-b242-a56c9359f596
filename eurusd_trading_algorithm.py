#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EUR/USD Trading Algorithm
-------------------------
This algorithm analyzes EUR/USD price data using RSI and MACD indicators
to generate buy and sell signals for trading opportunities.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

class EURUSDTradingAlgorithm:
    def __init__(self, rsi_period=14, rsi_overbought=70, rsi_oversold=30,
                 macd_fast=12, macd_slow=26, macd_signal=9):
        """
        Initialize the trading algorithm with parameters for the indicators.
        
        Parameters:
        -----------
        rsi_period : int
            Period for RSI calculation
        rsi_overbought : int
            RSI level considered overbought
        rsi_oversold : int
            RSI level considered oversold
        macd_fast : int
            Fast period for MACD calculation
        macd_slow : int
            Slow period for MACD calculation
        macd_signal : int
            Signal period for MACD calculation
        """
        self.rsi_period = rsi_period
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.macd_fast = macd_fast
        self.macd_slow = macd_slow
        self.macd_signal = macd_signal
        self.data = None
        
    def fetch_data(self, period="1y", interval="15m"):
        """
        Fetch EUR/USD data for the specified period and interval.
        
        Parameters:
        -----------
        period : str
            Time period to fetch data for (e.g., '1d', '1mo', '1y')
        interval : str
            Time interval between data points (e.g., '1m', '15m', '1h', '1d')
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame containing the fetched data
        """
        print(f"Fetching EUR/USD data for period: {period}, interval: {interval}...")
        try:
            # Use EURUSD=X ticker for EUR/USD data
            data = yf.download("EURUSD=X", period=period, interval=interval)
            if data.empty:
                raise ValueError("No data fetched. Check your internet connection or try a different period/interval.")
            
            self.data = data
            print(f"Successfully fetched {len(data)} data points.")
            return data
        except Exception as e:
            print(f"Error fetching data: {e}")
            return None
    
    def calculate_indicators(self, data=None):
        """
        Calculate RSI and MACD indicators for the given data.
        
        Parameters:
        -----------
        data : pandas.DataFrame, optional
            Data to calculate indicators for. If None, use self.data.
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame with added indicator columns
        """
        if data is None:
            if self.data is None:
                raise ValueError("No data available. Fetch data first.")
            data = self.data.copy()
        
        # Calculate RSI
        delta = data['Close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=self.rsi_period).mean()
        avg_loss = loss.rolling(window=self.rsi_period).mean()
        
        rs = avg_gain / avg_loss
        data['RSI'] = 100 - (100 / (1 + rs))
        
        # Calculate MACD
        ema_fast = data['Close'].ewm(span=self.macd_fast, adjust=False).mean()
        ema_slow = data['Close'].ewm(span=self.macd_slow, adjust=False).mean()
        data['MACD'] = ema_fast - ema_slow
        data['MACD_Signal'] = data['MACD'].ewm(span=self.macd_signal, adjust=False).mean()
        data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']
        
        return data
    
    def generate_signals(self, data=None):
        """
        Generate buy and sell signals based on RSI and MACD indicators.
        
        Parameters:
        -----------
        data : pandas.DataFrame, optional
            Data with indicators to generate signals for. If None, calculate indicators for self.data.
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame with added signal columns
        """
        if data is None:
            if self.data is None:
                raise ValueError("No data available. Fetch data first.")
            data = self.calculate_indicators()
        
        # Initialize signal columns
        data['RSI_Signal'] = 0  # 1 for buy, -1 for sell, 0 for neutral
        data['MACD_Signal_Line'] = 0  # 1 for buy, -1 for sell, 0 for neutral
        data['Combined_Signal'] = 0  # 1 for buy, -1 for sell, 0 for neutral
        data['Signal_Strength'] = 0  # 0-100 scale
        
        # Generate RSI signals
        data.loc[data['RSI'] < self.rsi_oversold, 'RSI_Signal'] = 1  # Buy signal (oversold)
        data.loc[data['RSI'] > self.rsi_overbought, 'RSI_Signal'] = -1  # Sell signal (overbought)
        
        # Generate MACD signals
        data['MACD_Signal_Line'] = np.where(data['MACD'] > data['MACD_Signal'], 1, 
                                          np.where(data['MACD'] < data['MACD_Signal'], -1, 0))
        
        # Generate combined signals
        # Strong buy: RSI oversold and MACD bullish crossover
        # Strong sell: RSI overbought and MACD bearish crossover
        data['Combined_Signal'] = data['RSI_Signal'] + data['MACD_Signal_Line']
        
        # Calculate signal strength (0-100 scale)
        # For buy signals: Higher when RSI is lower and MACD histogram is more positive
        # For sell signals: Higher when RSI is higher and MACD histogram is more negative
        
        # Normalize RSI for buy signals (lower RSI = stronger buy)
        rsi_buy_strength = (self.rsi_oversold - data['RSI']) / self.rsi_oversold * 50
        rsi_buy_strength = rsi_buy_strength.clip(0, 50)  # Clip to 0-50 range
        
        # Normalize RSI for sell signals (higher RSI = stronger sell)
        rsi_sell_strength = (data['RSI'] - self.rsi_overbought) / (100 - self.rsi_overbought) * 50
        rsi_sell_strength = rsi_sell_strength.clip(0, 50)  # Clip to 0-50 range
        
        # Normalize MACD histogram
        macd_hist_max = data['MACD_Histogram'].abs().max()
        macd_strength = data['MACD_Histogram'] / macd_hist_max * 50
        macd_strength = macd_strength.clip(-50, 50)  # Clip to -50 to 50 range
        
        # Combine strengths
        data.loc[data['Combined_Signal'] > 0, 'Signal_Strength'] = rsi_buy_strength + macd_strength
        data.loc[data['Combined_Signal'] < 0, 'Signal_Strength'] = rsi_sell_strength - macd_strength
        data['Signal_Strength'] = data['Signal_Strength'].abs().clip(0, 100)
        
        # Entry points are where the combined signal changes from 0 to non-zero
        data['Entry_Signal'] = 0
        data.loc[(data['Combined_Signal'].shift(1) == 0) & (data['Combined_Signal'] > 0), 'Entry_Signal'] = 1  # Buy entry
        data.loc[(data['Combined_Signal'].shift(1) == 0) & (data['Combined_Signal'] < 0), 'Entry_Signal'] = -1  # Sell entry
        
        # Also consider strong reversals
        data.loc[(data['Combined_Signal'].shift(1) < 0) & (data['Combined_Signal'] > 0), 'Entry_Signal'] = 1  # Buy entry after sell
        data.loc[(data['Combined_Signal'].shift(1) > 0) & (data['Combined_Signal'] < 0), 'Entry_Signal'] = -1  # Sell entry after buy
        
        return data
    
    def plot_signals(self, data=None, start_date=None, end_date=None):
        """
        Plot price, indicators, and signals.
        
        Parameters:
        -----------
        data : pandas.DataFrame, optional
            Data with signals to plot. If None, generate signals for self.data.
        start_date : str, optional
            Start date for plotting in 'YYYY-MM-DD' format
        end_date : str, optional
            End date for plotting in 'YYYY-MM-DD' format
        """
        if data is None:
            if self.data is None:
                raise ValueError("No data available. Fetch data first.")
            data = self.generate_signals()
        
        # Filter data by date range if specified
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]
        
        # Create figure with subplots
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12), sharex=True, gridspec_kw={'height_ratios': [2, 1, 1]})
        
        # Plot price
        ax1.plot(data.index, data['Close'], label='EUR/USD', color='blue')
        ax1.set_title('EUR/USD Price with Buy/Sell Signals')
        ax1.set_ylabel('Price')
        ax1.grid(True)
        
        # Plot buy signals
        buy_signals = data[data['Entry_Signal'] == 1]
        ax1.scatter(buy_signals.index, buy_signals['Close'], marker='^', color='green', s=100, label='Buy Signal')
        
        # Plot sell signals
        sell_signals = data[data['Entry_Signal'] == -1]
        ax1.scatter(sell_signals.index, sell_signals['Close'], marker='v', color='red', s=100, label='Sell Signal')
        
        ax1.legend()
        
        # Plot RSI
        ax2.plot(data.index, data['RSI'], label='RSI', color='purple')
        ax2.axhline(y=self.rsi_overbought, color='red', linestyle='--', label=f'Overbought ({self.rsi_overbought})')
        ax2.axhline(y=self.rsi_oversold, color='green', linestyle='--', label=f'Oversold ({self.rsi_oversold})')
        ax2.axhline(y=50, color='gray', linestyle='-', alpha=0.3)
        ax2.set_title('Relative Strength Index (RSI)')
        ax2.set_ylabel('RSI')
        ax2.grid(True)
        ax2.legend()
        ax2.set_ylim(0, 100)
        
        # Plot MACD
        ax3.plot(data.index, data['MACD'], label='MACD', color='blue')
        ax3.plot(data.index, data['MACD_Signal'], label='Signal Line', color='red')
        ax3.bar(data.index, data['MACD_Histogram'], label='Histogram', color='gray', alpha=0.3)
        ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
        ax3.set_title('Moving Average Convergence Divergence (MACD)')
        ax3.set_ylabel('MACD')
        ax3.grid(True)
        ax3.legend()
        
        plt.tight_layout()
        plt.show()
        
    def backtest(self, initial_capital=10000, position_size=0.1):
        """
        Backtest the trading strategy.
        
        Parameters:
        -----------
        initial_capital : float
            Initial capital for backtesting
        position_size : float
            Fraction of capital to use for each trade (0-1)
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame with backtest results
        """
        if self.data is None:
            raise ValueError("No data available. Fetch data first.")
        
        data = self.generate_signals()
        
        # Initialize backtest columns
        data['Position'] = 0  # 1 for long, -1 for short, 0 for no position
        data['Entry_Price'] = 0.0
        data['Exit_Price'] = 0.0
        data['Trade_Return'] = 0.0
        data['Capital'] = initial_capital
        
        position = 0
        entry_price = 0
        
        # Simulate trading
        for i in range(1, len(data)):
            # Update position based on entry signals
            if data.iloc[i-1]['Entry_Signal'] == 1 and position <= 0:  # Buy signal
                position = 1
                entry_price = data.iloc[i]['Open']
                data.iloc[i:, data.columns.get_loc('Position')] = position
                data.iloc[i:, data.columns.get_loc('Entry_Price')] = entry_price
            
            elif data.iloc[i-1]['Entry_Signal'] == -1 and position >= 0:  # Sell signal
                position = -1
                entry_price = data.iloc[i]['Open']
                data.iloc[i:, data.columns.get_loc('Position')] = position
                data.iloc[i:, data.columns.get_loc('Entry_Price')] = entry_price
            
            # Calculate returns for closed positions
            if (position == 1 and data.iloc[i-1]['Entry_Signal'] == -1) or \
               (position == -1 and data.iloc[i-1]['Entry_Signal'] == 1):
                exit_price = data.iloc[i]['Open']
                data.iloc[i, data.columns.get_loc('Exit_Price')] = exit_price
                
                # Calculate trade return
                if position == 1:  # Long position
                    trade_return = (exit_price - entry_price) / entry_price
                else:  # Short position
                    trade_return = (entry_price - exit_price) / entry_price
                
                data.iloc[i, data.columns.get_loc('Trade_Return')] = trade_return
                
                # Update capital
                trade_amount = data.iloc[i-1]['Capital'] * position_size
                trade_profit = trade_amount * trade_return
                data.iloc[i:, data.columns.get_loc('Capital')] = data.iloc[i-1]['Capital'] + trade_profit
        
        # Calculate performance metrics
        total_trades = len(data[data['Trade_Return'] != 0])
        winning_trades = len(data[data['Trade_Return'] > 0])
        losing_trades = len(data[data['Trade_Return'] < 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_win = data[data['Trade_Return'] > 0]['Trade_Return'].mean() if winning_trades > 0 else 0
        avg_loss = data[data['Trade_Return'] < 0]['Trade_Return'].mean() if losing_trades > 0 else 0
        
        final_capital = data.iloc[-1]['Capital']
        total_return = (final_capital - initial_capital) / initial_capital * 100
        
        print("\n===== Backtest Results =====")
        print(f"Initial Capital: ${initial_capital:.2f}")
        print(f"Final Capital: ${final_capital:.2f}")
        print(f"Total Return: {total_return:.2f}%")
        print(f"Total Trades: {total_trades}")
        print(f"Winning Trades: {winning_trades} ({win_rate*100:.2f}%)")
        print(f"Losing Trades: {losing_trades} ({(1-win_rate)*100:.2f}%)")
        print(f"Average Win: {avg_win*100:.2f}%")
        print(f"Average Loss: {avg_loss*100:.2f}%")
        
        return data
    
    def monitor_current_market(self, interval_minutes=15):
        """
        Monitor the current market and provide real-time trading recommendations.
        
        Parameters:
        -----------
        interval_minutes : int
            Interval in minutes to refresh data
        """
        print("Starting real-time market monitoring...")
        print(f"Checking for signals every {interval_minutes} minutes.")
        print("Press Ctrl+C to stop monitoring.")
        
        try:
            while True:
                # Fetch the latest data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)  # Get 7 days of data for better indicator calculation
                
                # Use a shorter interval for more frequent updates
                data = self.fetch_data(period="7d", interval=f"{interval_minutes}m")
                
                if data is not None:
                    # Generate signals
                    data = self.generate_signals()
                    
                    # Get the latest data point
                    latest = data.iloc[-1]
                    prev = data.iloc[-2] if len(data) > 1 else None
                    
                    # Print current market status
                    print("\n===== Current Market Status =====")
                    print(f"Time: {latest.name}")
                    print(f"EUR/USD Price: {latest['Close']:.5f}")
                    print(f"RSI: {latest['RSI']:.2f}")
                    print(f"MACD: {latest['MACD']:.5f}")
                    print(f"MACD Signal: {latest['MACD_Signal']:.5f}")
                    print(f"MACD Histogram: {latest['MACD_Histogram']:.5f}")
                    
                    # Check for entry signals
                    if latest['Entry_Signal'] == 1:
                        print("\n🟢 BUY SIGNAL DETECTED 🟢")
                        print(f"Signal Strength: {latest['Signal_Strength']:.2f}/100")
                        print(f"Reason: {'RSI Oversold' if latest['RSI'] < self.rsi_oversold else ''} "
                              f"{'MACD Bullish Crossover' if latest['MACD'] > latest['MACD_Signal'] else ''}")
                        
                    elif latest['Entry_Signal'] == -1:
                        print("\n🔴 SELL SIGNAL DETECTED 🔴")
                        print(f"Signal Strength: {latest['Signal_Strength']:.2f}/100")
                        print(f"Reason: {'RSI Overbought' if latest['RSI'] > self.rsi_overbought else ''} "
                              f"{'MACD Bearish Crossover' if latest['MACD'] < latest['MACD_Signal'] else ''}")
                        
                    else:
                        print("\n⚪ NO CLEAR SIGNAL ⚪")
                        
                        # Provide additional market insights
                        if latest['RSI'] > 50 and latest['MACD'] > 0:
                            print("Market Bias: Bullish (RSI > 50, MACD > 0)")
                        elif latest['RSI'] < 50 and latest['MACD'] < 0:
                            print("Market Bias: Bearish (RSI < 50, MACD < 0)")
                        else:
                            print("Market Bias: Mixed/Neutral")
                    
                # Wait for the next interval
                print(f"\nWaiting {interval_minutes} minutes for next update...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\nStopped monitoring.")


# Example usage
if __name__ == "__main__":
    # Create the trading algorithm
    algo = EURUSDTradingAlgorithm()
    
    # Fetch data
    data = algo.fetch_data(period="6mo", interval="15m")
    
    # Generate signals
    signals = algo.generate_signals()
    
    # Plot signals
    algo.plot_signals()
    
    # Backtest the strategy
    backtest_results = algo.backtest()
    
    # Monitor current market (uncomment to run)
    # algo.monitor_current_market(interval_minutes=15)
