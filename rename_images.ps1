# Script to rename image files in the MV Morning folder
$folderPath = "C:\Users\<USER>\Desktop\MV Morning"

# Get all image files in the folder
$imageFiles = Get-ChildItem -Path $folderPath -Filter "*.jpg" | Sort-Object Name

# Rename files with sequential numbers
$counter = 1
foreach ($file in $imageFiles) {
    $newName = "$counter.jpg"
    $newPath = Join-Path -Path $folderPath -ChildPath $newName
    
    # Check if the destination file already exists
    if (Test-Path -Path $newPath) {
        Write-Host "File $newName already exists. Skipping..." -ForegroundColor Yellow
        continue
    }
    
    # Rename the file
    Write-Host "Renaming $($file.Name) to $newName" -ForegroundColor Green
    Rename-Item -Path $file.FullName -NewName $newName
    
    $counter++
}

Write-Host "`nRenamed $($counter - 1) files successfully!" -ForegroundColor Cyan
