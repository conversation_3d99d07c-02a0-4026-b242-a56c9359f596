# Windows Control Center - User Guide

## Introduction

Windows Control Center is a powerful application that provides centralized control over Windows system and applications. This guide will help you understand how to use each feature of the application.

## Getting Started

1. Launch the application by running `WindowsControlCenter.exe` or using the `CompileAndRun.bat` script
2. For full functionality, run the application as administrator
3. The application is organized into tabs, each focusing on a different aspect of system management

## Running Applications Tab

This tab allows you to manage all applications with visible windows.

### Features:

- **View Applications**: See all running applications with window titles
- **Filter**: Type in the filter box to find specific applications
- **Bring to Front**: Select an application and click "Bring to Front" to make it visible
- **Minimize/Maximize**: Control window state of selected applications
- **Close App**: Safely close the selected application

### Tips:

- The memory column shows real-time memory usage
- Closing applications through this interface is equivalent to clicking the X button

## System Information Tab

This tab provides real-time information about your system's hardware and operating system.

### Features:

- **CPU Information**: View processor name, core count, and current usage
- **Memory Information**: Monitor total, available, and used memory
- **Operating System Information**: View OS details and system uptime
- **Refresh**: Update all information with current values

### Tips:

- The progress bars change color based on usage levels (green, orange, red)
- System information refreshes automatically every few seconds

## Services Tab

This tab allows you to view and manage Windows services.

### Features:

- **View Services**: See all Windows services with their status and startup type
- **Filter**: Use the dropdown to filter by status or startup type
- **Search**: Find services by name or description
- **Start/Stop/Restart**: Control service state (requires admin privileges)
- **Change Startup Type**: Modify how services start with Windows (requires admin privileges)

### Tips:

- Red warning text indicates when administrator privileges are required
- Always be careful when stopping system services

## Network Tab

This tab provides tools for managing and monitoring network connections.

### Adapters Sub-tab:

- **View Adapters**: See all network adapters with their status and configuration
- **Enable/Disable**: Control adapter state (requires admin privileges)
- **Renew DHCP**: Refresh IP address assignment (requires admin privileges)
- **Details**: View comprehensive adapter information

### Statistics Sub-tab:

- **Traffic Statistics**: Monitor data sent and received
- **Connection Count**: See number of active network connections
- **Flush DNS**: Clear DNS resolver cache (requires admin privileges)

### Tools Sub-tab:

- **Ping Tool**: Test connectivity to hosts
- **Configure Ping**: Set ping count (4, 8, 16, 32, or 64)
- **View Results**: See detailed ping statistics and individual ping results

### Tips:

- The ping tool shows min/avg/max response times
- Success rate is color-coded (green for full success, orange for partial, red for failure)

## Automation Tab

This tab allows you to record and replay keyboard and mouse actions.

### Features:

- **Record**: Capture keyboard and mouse actions
- **Stop Recording**: End the capture session
- **Play Macro**: Replay recorded actions (demo only in this version)
- **Save/Load Macro**: Store and retrieve recorded macros
- **Clear**: Remove all recorded actions

### Tips:

- Recording captures both keyboard and mouse events
- Timestamps show when each action occurred

## Task Scheduler Tab

This tab provides a simple interface for scheduled tasks.

### Features:

- **View Tasks**: See sample scheduled tasks
- **Add/Edit/Delete**: Task management (demo only in this version)
- **Run Now**: Execute tasks on demand (demo only in this version)

### Tips:

- This tab demonstrates the UI for a task scheduler but has limited functionality in this version

## Administrator Privileges

Many features in Windows Control Center require administrator privileges to function properly:

- Starting, stopping, or restarting services
- Changing service startup types
- Enabling or disabling network adapters
- Renewing DHCP leases
- Flushing DNS cache

If you see a red warning message, it indicates that administrator privileges are required for full functionality.

## Troubleshooting

### Application Won't Start

- Ensure you have .NET 6.0 Runtime or SDK installed
- Try running as administrator
- Check Windows Event Viewer for error details

### Features Not Working

- For service or network management issues, ensure you're running as administrator
- Some features may be limited by Windows security policies
- Check for red warning messages indicating privilege requirements

### Performance Issues

- Close the application and restart it
- Check for other resource-intensive applications running
- Ensure your system meets the minimum requirements

## Feedback and Support

We welcome your feedback on Windows Control Center. Please use the TESTING.md file to provide structured feedback and report any issues you encounter.

Thank you for using Windows Control Center!
