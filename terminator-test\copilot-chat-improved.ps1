# Improved PowerShell script to open Windows Copilot and chat with it

# Add reference to Windows Forms for SendKeys and System.Drawing for screenshots
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class KeyboardSend
{
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    [DllImport("user32.dll")]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
    
    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);
    
    public const int KEYEVENTF_EXTENDEDKEY = 0x0001;
    public const int KEYEVENTF_KEYUP = 0x0002;
    
    public const byte VK_LWIN = 0x5B;  // Left Windows key
    public const byte VK_C = 0x43;     // C key
    public const byte VK_RETURN = 0x0D; // Enter key
    public const byte VK_ESCAPE = 0x1B; // Escape key
    
    public static void SendWinC()
    {
        // Press Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Press C key
        keybd_event(VK_C, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release C key
        keybd_event(VK_C, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static void SendEnter()
    {
        // Press Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static void SendEscape()
    {
        // Press Escape key
        keybd_event(VK_ESCAPE, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Escape key
        keybd_event(VK_ESCAPE, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static bool ActivateWindowByPartialTitle(string partialTitle)
    {
        // This is a simplified approach - in a real application, you would enumerate all windows
        IntPtr hWnd = FindWindow(null, partialTitle);
        if (hWnd != IntPtr.Zero)
        {
            return SetForegroundWindow(hWnd);
        }
        return false;
    }
}
"@

# Function to take a screenshot
function Take-Screenshot {
    param (
        [string]$FilePath
    )
    
    $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
    $bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.Size)
    $bitmap.Save($FilePath)
    $graphics.Dispose()
    $bitmap.Dispose()
    
    Write-Host "Screenshot saved to $FilePath"
}

# Function to wait for a specified time
function Wait-Time {
    param (
        [int]$Milliseconds
    )
    
    Start-Sleep -Milliseconds $Milliseconds
}

# Function to try multiple methods to open Copilot
function Open-Copilot {
    # Method 1: Win+C shortcut
    Write-Host "Trying to open Windows Copilot using Win+C shortcut..."
    [KeyboardSend]::SendWinC()
    Wait-Time 3000
    
    # Take a screenshot to see if Copilot opened
    Take-Screenshot -FilePath "terminator-test\copilot_method1.png"
    
    # Method 2: Try to find and activate Copilot window if it's already open
    Write-Host "Trying to find and activate existing Copilot window..."
    $activated = [KeyboardSend]::ActivateWindowByPartialTitle("Copilot")
    if ($activated) {
        Write-Host "Found and activated existing Copilot window"
        Wait-Time 1000
        Take-Screenshot -FilePath "terminator-test\copilot_method2.png"
        return $true
    }
    
    # Method 3: Search for Copilot in Start menu
    Write-Host "Trying to open Copilot via Start menu search..."
    # Press Win key to open Start menu
    [KeyboardSend]::keybd_event([KeyboardSend]::VK_LWIN, 0, [KeyboardSend]::KEYEVENTF_EXTENDEDKEY, [System.UIntPtr]::Zero)
    [KeyboardSend]::keybd_event([KeyboardSend]::VK_LWIN, 0, [KeyboardSend]::KEYEVENTF_KEYUP | [KeyboardSend]::KEYEVENTF_EXTENDEDKEY, [System.UIntPtr]::Zero)
    Wait-Time 1000
    
    # Type "Copilot"
    [System.Windows.Forms.SendKeys]::SendWait("Copilot")
    Wait-Time 1000
    
    # Take a screenshot to see search results
    Take-Screenshot -FilePath "terminator-test\copilot_search.png"
    
    # Press Enter to open the top result
    [KeyboardSend]::SendEnter()
    Wait-Time 3000
    
    # Take a screenshot to see if Copilot opened
    Take-Screenshot -FilePath "terminator-test\copilot_method3.png"
    
    return $true
}

# Messages to send to Copilot
$messages = @(
    "Hello Copilot! I'm chatting with you through an automated script.",
    "What's the weather like today?",
    "Tell me a joke"
)

# Try to open Copilot
$success = Open-Copilot

if ($success) {
    Write-Host "Copilot should be open now. Proceeding with chat..."
    
    # Send each message and wait for a response
    foreach ($message in $messages) {
        Write-Host "Sending message: $message"
        
        # Clear any existing text (just in case)
        [System.Windows.Forms.SendKeys]::SendWait("^a")  # Ctrl+A to select all
        Wait-Time 500
        [System.Windows.Forms.SendKeys]::SendWait("{DELETE}")  # Delete selected text
        Wait-Time 500
        
        # Type the message
        [System.Windows.Forms.SendKeys]::SendWait($message)
        Wait-Time 1000
        
        # Take a screenshot before sending
        Take-Screenshot -FilePath "terminator-test\before_send_$($messages.IndexOf($message)).png"
        
        # Send Enter to submit the message
        [KeyboardSend]::SendEnter()
        
        # Wait for Copilot to respond
        Write-Host "Waiting for Copilot to respond..."
        Wait-Time 5000
        
        # Take a screenshot after response
        Take-Screenshot -FilePath "terminator-test\after_response_$($messages.IndexOf($message)).png"
    }
    
    Write-Host "Chat with Copilot completed!"
} else {
    Write-Host "Failed to open Copilot after multiple attempts."
}

# Take a final screenshot
Take-Screenshot -FilePath "terminator-test\final_state.png"
