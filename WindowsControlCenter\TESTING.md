# Windows Control Center - Testing Guide

This document provides instructions for testing the Windows Control Center application and collecting feedback for future improvements.

## Prerequisites

- Windows 10 or 11
- .NET 6.0 SDK or later (for building from source)
- Administrator privileges (for full functionality)

## Building and Running

1. Run the `CompileAndRun.bat` file in the root directory
2. If you don't have the .NET SDK installed, you'll need to install it from https://dotnet.microsoft.com/download
3. For full functionality (especially for network and service management), run as administrator

## Test Cases

Please test the following features and note any issues or suggestions for improvement:

### Running Applications Tab

- [ ] View list of running applications
- [ ] Filter applications by name or window title
- [ ] Bring an application to front
- [ ] Minimize an application
- [ ] Maximize an application
- [ ] Close an application

**Notes:**
- 
- 

### System Information Tab

- [ ] View CPU information (name, cores, usage)
- [ ] View memory information (total, available, usage)
- [ ] View operating system information (name, version, uptime)
- [ ] Refresh system information

**Notes:**
- 
- 

### Services Tab

- [ ] View list of Windows services
- [ ] Filter services by status (Running, Stopped)
- [ ] Filter services by startup type (Automatic, Manual, Disabled)
- [ ] Search for a service by name or description
- [ ] Start a stopped service (requires admin)
- [ ] Stop a running service (requires admin)
- [ ] Restart a service (requires admin)
- [ ] Change a service's startup type (requires admin)

**Notes:**
- 
- 

### Network Tab - Adapters

- [ ] View list of network adapters
- [ ] View adapter details (IP, MAC, type, status)
- [ ] Enable a disabled adapter (requires admin)
- [ ] Disable an enabled adapter (requires admin)
- [ ] Renew DHCP lease (requires admin)
- [ ] View detailed adapter information

**Notes:**
- 
- 

### Network Tab - Statistics

- [ ] View network traffic statistics
- [ ] View active connections count
- [ ] Refresh network statistics
- [ ] Flush DNS cache (requires admin)

**Notes:**
- 
- 

### Network Tab - Tools

- [ ] Ping a host (e.g., ******* or google.com)
- [ ] Change ping count and observe results
- [ ] View detailed ping results
- [ ] Test pinging an invalid host

**Notes:**
- 
- 

### Automation Tab

- [ ] Start recording mouse and keyboard actions
- [ ] Record some simple actions
- [ ] Stop recording
- [ ] View recorded actions
- [ ] Clear recorded actions

**Notes:**
- 
- 

### Task Scheduler Tab

- [ ] View sample scheduled tasks
- [ ] Test task-related buttons (note: full functionality is not implemented in this demo)

**Notes:**
- 
- 

## Performance Testing

- [ ] Application startup time
- [ ] Responsiveness when switching between tabs
- [ ] Memory usage during extended use
- [ ] CPU usage during normal operation

**Notes:**
- 
- 

## Usability Feedback

1. Which features did you find most useful?

2. Which features were difficult to use or understand?

3. What additional features would you like to see?

4. How would you rate the overall user interface (1-10)?

5. Any suggestions for improving the user experience?

## Bug Reports

Please provide details of any bugs encountered:

1. **Feature/Tab:**
   **Description:**
   **Steps to reproduce:**
   **Expected behavior:**
   **Actual behavior:**

2. **Feature/Tab:**
   **Description:**
   **Steps to reproduce:**
   **Expected behavior:**
   **Actual behavior:**

## Submission

Please save this file with your feedback and send it to the development team.

Thank you for helping improve Windows Control Center!
