// Test script to interact with Notepad
const fetch = require('node-fetch');

// Function to make a request to the Terminator server
async function makeRequest(endpoint, data = {}) {
  try {
    const response = await fetch(`http://localhost:9375${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error);
    throw error;
  }
}

// Find Notepad window
async function findNotepad() {
  console.log('Finding Notepad window...');
  try {
    const result = await makeRequest('/find_window', {
      titleContains: 'Notepad',
      timeout_ms: 5000
    });
    
    if (!result || result.error) {
      console.error('Could not find Notepad window');
      return null;
    }
    
    console.log('Found Notepad window:', result);
    return result;
  } catch (error) {
    console.error('Error finding Notepad:', error);
    return null;
  }
}

// Type text into Notepad
async function typeIntoNotepad(windowSelector, text) {
  console.log(`Typing text into Notepad: "${text}"...`);
  try {
    // First, we need to click on the editor area
    const clickResult = await makeRequest('/click', {
      selector_chain: [windowSelector],
      timeout_ms: 5000
    });
    
    console.log('Click result:', clickResult);
    
    // Then type the text
    const typeResult = await makeRequest('/type_text', {
      selector_chain: [windowSelector],
      text: text,
      timeout_ms: 5000
    });
    
    console.log('Type result:', typeResult);
    return typeResult;
  } catch (error) {
    console.error('Error typing into Notepad:', error);
    return null;
  }
}

// Main function
async function main() {
  try {
    // Find Notepad window
    const notepadWindow = await findNotepad();
    if (!notepadWindow) {
      console.log('Could not find Notepad window. Make sure Notepad is open.');
      return;
    }
    
    // Get the selector for the Notepad window
    const windowSelector = notepadWindow.suggested_selector || `window:"Notepad"`;
    
    // Type text into Notepad
    await typeIntoNotepad(windowSelector, 'Hello from Terminator MCP Agent! This text was typed automatically.');
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
main();
