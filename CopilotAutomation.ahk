#NoEnv  ; Recommended for performance and compatibility with future AutoHotkey releases.
SendMode Input  ; Recommended for new scripts due to its superior speed and reliability.
SetWorkingDir %A_ScriptDir%  ; Ensures a consistent starting directory.

; Define the message to send to Copilot
CopilotMessage := "Tell me about the evolution of CPUs and how they have affected AI technology development. How have advancements in processors enabled the current AI revolution?"

; Function to activate Windows Copilot and send a message
SendToCopilot(message) {
    ; Try to activate Windows Copilot
    WinActivate, ahk_exe Copilot.exe
    if !WinActive("ahk_exe Copilot.exe") {
        ; If Copilot.exe doesn't work, try the Microsoft Edge version
        WinActivate, ahk_exe msedge.exe
        if !WinActive("ahk_exe msedge.exe") {
            MsgBox, Could not find Windows Copilot window. Please make sure it's open.
            return
        }
    }
    
    ; Wait a bit for the window to be fully active
    Sleep, 500
    
    ; Send the message
    SendInput, %message%
    Sleep, 200
    Send, {Enter}
}

; Hotkey to send the message (Ctrl+Alt+C)
^!c::
    SendToCopilot(CopilotMessage)
    return

; Run the script once when started
SendT<PERSON><PERSON>opilot(CopilotMessage)
