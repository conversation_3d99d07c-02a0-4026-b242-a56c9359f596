# Script to get the current tab URL from Microsoft Edge
Add-Type -AssemblyName System.Windows.Forms

Write-Host "Attempting to get current tab URL from Microsoft Edge..." -ForegroundColor Cyan

# Find Edge window
$edgeProcess = Get-Process msedge | Where-Object { $_.MainWindowTitle -ne "" } | Select-Object -First 1

if ($null -eq $edgeProcess) {
    Write-Host "No Microsoft Edge window found with a title." -ForegroundColor Yellow
    exit
}

Write-Host "Found Microsoft Edge window: $($edgeProcess.MainWindowTitle)" -ForegroundColor Green

# Try to activate the window
$wshell = New-Object -ComObject wscript.shell
$result = $wshell.AppActivate($edgeProcess.MainWindowTitle)

if (-not $result) {
    Write-Host "Could not activate Edge window." -ForegroundColor Red
    exit
}

# Clear clipboard
[System.Windows.Forms.Clipboard]::Clear()
Start-Sleep -Milliseconds 500

# Send Ctrl+L to select address bar
$wshell.SendKeys("^l")
Start-Sleep -Milliseconds 500

# Send Ctrl+C to copy
$wshell.SendKeys("^c")
Start-Sleep -Milliseconds 500

# Get text from clipboard
$url = [System.Windows.Forms.Clipboard]::GetText()

if ([string]::IsNullOrEmpty($url)) {
    Write-Host "Could not retrieve URL from clipboard." -ForegroundColor Red
} else {
    Write-Host "Current tab URL: $url" -ForegroundColor Green
}

# Try to get tab count from window title
$title = $edgeProcess.MainWindowTitle
if ($title -match "and (\d+) more pages") {
    $tabCount = [int]$matches[1] + 1
    Write-Host "Based on window title, there are approximately $tabCount tabs open." -ForegroundColor Yellow
}
