# PowerShell AI Chat Client using OpenRouter API

# Function to get API key
function Get-ApiKey {
    $apiKey = $env:OPENROUTER_API_KEY
    if (-not $apiKey) {
        Write-Host "OpenRouter API key not found in environment variables."
        $apiKey = Read-Host "Please enter your OpenRouter API key"
        $env:OPENROUTER_API_KEY = $apiKey
    }
    return $apiKey
}

# Function to send message to AI
function Send-Message {
    param (
        [array]$Messages,
        [string]$Model = "anthropic/claude-3-haiku",
        [string]$ApiKey = $null
    )
    
    if (-not $ApiKey) {
        $ApiKey = Get-ApiKey
    }
    
    $headers = @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $ApiKey"
        "HTTP-Referer" = "https://terminal-chat.local"
        "X-Title" = "PowerShell AI Chat"
    }
    
    $body = @{
        model = $Model
        messages = $Messages
        stream = $false
    } | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-RestMethod -Uri "https://openrouter.ai/api/v1/chat/completions" -Method Post -Headers $headers -Body $body -ContentType "application/json"
        return $response.choices[0].message.content
    }
    catch {
        Write-Host "Error: $_" -ForegroundColor Red
        return "Error communicating with AI service."
    }
}

# Main function
function Start-AiChat {
    param (
        [string]$Model = "anthropic/claude-3-haiku",
        [string]$SystemMessage = $null
    )
    
    Clear-Host
    Write-Host "========================================"
    Write-Host "       PowerShell AI Chat Client        " -ForegroundColor Cyan
    Write-Host "========================================"
    Write-Host "Using model: $Model" -ForegroundColor Yellow
    Write-Host "Type 'exit' or 'quit' to end the conversation"
    Write-Host "========================================"
    Write-Host ""
    
    # Initialize conversation
    $conversation = @()
    
    # Add system message if provided
    if ($SystemMessage) {
        $systemMsg = @{
            role = "system"
            content = $SystemMessage
        }
        $conversation += $systemMsg
        Write-Host "System: $SystemMessage" -ForegroundColor Yellow
    }
    
    # Main chat loop
    while ($true) {
        # Get user input
        Write-Host "You: " -ForegroundColor Blue -NoNewline
        $userInput = Read-Host
        
        # Check for exit command
        if ($userInput -eq "exit" -or $userInput -eq "quit") {
            break
        }
        
        # Add user message to conversation
        $userMsg = @{
            role = "user"
            content = $userInput
        }
        $conversation += $userMsg
        
        Write-Host "AI is thinking..." -ForegroundColor Gray
        
        # Get AI response
        $aiResponse = Send-Message -Messages $conversation -Model $Model
        
        # Add AI response to conversation
        $aiMsg = @{
            role = "assistant"
            content = $aiResponse
        }
        $conversation += $aiMsg
        
        # Print the response
        Write-Host "AI: " -ForegroundColor Green -NoNewline
        Write-Host $aiResponse
        Write-Host ""
    }
}

# Parse command line arguments
param (
    [string]$Model = "anthropic/claude-3-haiku",
    [string]$System
)

# Start the chat
Start-AiChat -Model $Model -SystemMessage $System
