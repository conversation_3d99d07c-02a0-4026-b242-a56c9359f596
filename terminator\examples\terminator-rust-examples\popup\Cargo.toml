[package]
name = "popup"
version = "0.1.0"
edition = "2021"

[dependencies]
windows = { version = "0.61.1", features = [
    "Win32_Foundation",
    "Win32_Graphics_Gdi",
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_LibraryLoader", # For GetModuleHandleW
    "Win32_System_Threading",   # Potentially for thread IDs if needed
    "Win32_Globalization", # HSTRING often needs this or similar
    # "Win32_Graphics_Dwm",     # Not strictly needed for UpdateLayeredWindow
] }
terminator = { workspace = true }
tokio = { workspace = true }
once_cell = "1.19"
lazy_static = "1.4.0" # Alternative to once_cell::sync::Lazy for statics if preferred, but <PERSON><PERSON> is fine
anyhow = "1.0.98"
global-hotkey = "0.7.0"

