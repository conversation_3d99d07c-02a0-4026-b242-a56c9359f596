# Penetration Testing Reconnaissance and Relationship Mapping Plan

**Objective:** To guide you in performing initial reconnaissance on `sescotransegypt.com` and `sescotrans.net` and to map potential relationships between them using your Kali Linux system.

**Important Note:** All commands listed below are to be executed by *you* on your Kali Linux system. This plan provides the methodology and tools; you are responsible for the execution and interpretation of results.

## Phase 1: Initial Reconnaissance (Perform these steps for *both* `sescotransegypt.com` and `sescotrans.net`)

### 1.1. WHOIS Lookup
*   **Purpose:** To gather domain registration information.
*   **Command:**
    ```bash
    whois sescotransegypt.com
    whois sescotrans.net
    ```
*   **Look for:**
    *   Registrar information
    *   Registration, update, and expiry dates
    *   Name server details
    *   Registrant, administrative, and technical contact information (often redacted for privacy, but sometimes patterns or organizational names are visible).

### 1.2. DNS Enumeration
*   **Purpose:** To identify IP addresses, mail servers, name servers, and other DNS records associated with the domains.
*   **Commands (replace `[domain]` with `sescotransegypt.com` and then `sescotrans.net`):**
    *   Basic Lookup:
        ```bash
        nslookup [domain]
        host [domain]
        ```
    *   Specific Record Types (using `dig`):
        ```bash
        dig A [domain]         # IPv4 addresses
        dig AAAA [domain]      # IPv6 addresses (if any)
        dig MX [domain]        # Mail Exchange servers
        dig NS [domain]        # Name Servers
        dig TXT [domain]       # TXT records (SPF, DKIM, DMARC, site verification, etc.)
        dig ANY [domain]       # Attempts to get all available record types (may be restricted)
        ```
*   **Look for:**
    *   IP addresses (A and AAAA records) and associated netblocks.
    *   Mail server hostnames and priorities (MX records).
    *   Authoritative name servers (NS records).
    *   TXT records which might reveal email security policies, domain ownership verification tokens, or other services.

### 1.3. HTTP/HTTPS Headers & Basic Web Probing
*   **Purpose:** To gather information about the web servers and technologies in use.
*   **Commands (for both HTTP and HTTPS, for each domain):**
    ```bash
    curl -I http://sescotransegypt.com
    curl -I https://sescotransegypt.com
    curl -I http://sescotrans.net
    curl -I https://sescotrans.net
    ```
*   **Look for:**
    *   `Server` header (e.g., Apache, Nginx, IIS, LiteSpeed).
    *   `X-Powered-By` header (e.g., PHP version, ASP.NET).
    *   Cookies being set.
    *   Security headers (e.g., `Content-Security-Policy`, `Strict-Transport-Security`, `X-Frame-Options`, `X-Content-Type-Options`).
    *   Redirects (Location headers and status codes like 301, 302).
    *   Content Management System (CMS) specific headers or path structures if observable.

### 1.4. SSL/TLS Certificate Inspection
*   **Purpose:** To gather details about the SSL/TLS certificates used by the domains.
*   **Techniques:**
    1.  **Browser Method:** Navigate to `https://[domain]` in your web browser and inspect the certificate details (usually by clicking the padlock icon in the address bar).
    2.  **Command-Line Method (Kali Linux):**
        ```bash
        openssl s_client -connect sescotransegypt.com:443 -servername sescotransegypt.com | openssl x509 -text -noout
        openssl s_client -connect sescotrans.net:443 -servername sescotrans.net | openssl x509 -text -noout
        ```
        (Note: `-servername` is important for SNI, ensuring you get the certificate for the correct virtual host)
*   **Look for:**
    *   Certificate Issuer (Certificate Authority - CA).
    *   Validity Period (Not Before, Not After dates).
    *   Subject Common Name (CN) and Organization (O).
    *   Subject Alternative Names (SANs) – these can reveal other domains or subdomains covered by the same certificate.
    *   Certificate chain and trust.

### 1.5. Search Engine Dorking
*   **Purpose:** To find publicly accessible information, documents, subdomains, and potential vulnerabilities indexed by search engines.
*   **Technique:** Use search engines (Google, DuckDuckGo, Bing) with advanced search operators.
*   **Example Queries (run for both `sescotransegypt.com` and `sescotrans.net`):**
    ```
    site:sescotransegypt.com
    site:sescotrans.net
    site:sescotransegypt.com filetype:pdf
    site:sescotrans.net filetype:xls
    site:sescotransegypt.com intitle:"index of"
    site:sescotrans.net inurl:login
    related:sescotransegypt.com
    related:sescotrans.net
    ```
*   **Look for:**
    *   Exposed documents (PDFs, spreadsheets, text files).
    *   Subdomains indexed by search engines.
    *   Login portals.
    *   Directory listings.
    *   Error messages or debug information.
    *   Mentions of related entities or technologies.

### 1.6. Certificate Transparency (CT) Logs
*   **Purpose:** To discover all publicly trusted SSL/TLS certificates issued for the domains, which can reveal subdomains.
*   **Technique:** Use online CT log search tools.
*   **Examples:**
    *   `https://crt.sh/?q=%.sescotransegypt.com`
    *   `https://crt.sh/?q=%.sescotrans.net`
    *   Search on `censys.io` or other similar platforms.
*   **Look for:**
    *   A comprehensive list of current and historical certificates.
    *   Subdomains (e.g., `mail.example.com`, `dev.example.com`) that might not be found through other DNS enumeration methods.
    *   Issuance patterns or common CAs.

## Phase 2: Relationship Mapping (Analyzing and Correlating Data)

Once you have gathered information for both domains, analyze it to identify potential relationships:

### 2.1. Shared WHOIS Information:
*   Compare the WHOIS data. Look for identical or similar:
    *   Registrant names, organizations, email addresses (even if partially redacted, look for patterns).
    *   Administrative or technical contact details.
    *   Name servers listed in WHOIS records.
    *   Registration dates or registrars.

### 2.2. Shared IP Addresses or Netblocks:
*   Do the domains resolve to the same IP address(es)?
*   Are their IP addresses within the same network range (netblock/CIDR)? Use `whois [IP_ADDRESS]` on the resolved IPs to check netblock ownership and allocation details.
*   This could indicate shared hosting or infrastructure.

### 2.3. Common DNS Infrastructure:
*   Do they use the same Name Servers (NS records)?
*   Do they use the same Mail Exchange (MX) servers or mail providers?
*   Are there similar patterns in TXT records (e.g., same Google site verification codes, similar SPF `include:` mechanisms for the same third-party mail services)?

### 2.4. Cross-Links and Shared Web Content/Technologies:
*   Manually browse the websites. Look for:
    *   Direct links from one site to the other.
    *   Shared branding, logos, "About Us" information, contact details (phone numbers, physical addresses).
    *   Common third-party services (e.g., analytics scripts like Google Analytics - check for shared UA-XXXXX-Y IDs, social media widgets, CDNs).
    *   Similarities in website structure, design, or source code (view source).
    *   Identical or similar error pages or backend technology signatures identified from HTTP headers or website behavior.

### 2.5. Shared SSL/TLS Certificate Details:
*   Are the certificates for both domains issued by the same Certificate Authority (CA)?
*   Do they have overlapping validity periods or similar issuance patterns?
*   Is one domain listed as a Subject Alternative Name (SAN) on the other's certificate, or do they share SANs pointing to a common entity?
*   Are organizational details in the certificate subjects similar?

### 2.6. Correlated Findings from Public Sources:
*   Do subdomains discovered via CT logs or search engine dorking for one domain show any relation to the other (e.g., `portal.sescotransegypt.com` and `portal.sescotrans.net`)?
*   Do publicly exposed documents from one domain mention the other?

## Workflow Visualization:

```mermaid
graph TD
    A[Start Reconnaissance] --> B1[Target: sescotransegypt.com];
    A --> B2[Target: sescotrans.net];

    subgraph Recon for sescotransegypt.com
        B1 --> C1[WHOIS Lookup];
        B1 --> D1[DNS Enumeration];
        B1 --> E1[HTTP Headers];
        B1 --> F1[SSL/TLS Cert];
        B1 --> G1[Search Dorks];
        B1 --> H1[CT Logs];
    end

    subgraph Recon for sescotrans.net
        B2 --> C2[WHOIS Lookup];
        B2 --> D2[DNS Enumeration];
        B2 --> E2[HTTP Headers];
        B2 --> F2[SSL/TLS Cert];
        B2 --> G2[Search Dorks];
        B2 --> H2[CT Logs];
    end

    I[Gathered Data for sescotransegypt.com]
    J[Gathered Data for sescotrans.net]

    C1 --> I; D1 --> I; E1 --> I; F1 --> I; G1 --> I; H1 --> I;
    C2 --> J; D2 --> J; E2 --> J; F2 --> J; G2 --> J; H2 --> J;

    K[Relationship Mapping Analysis]
    I --> K;
    J --> K;

    K --> L1[Compare WHOIS];
    K --> L2[Compare IPs/Netblocks];
    K --> L3[Compare DNS Infra];
    K --> L4[Compare Web Content/Tech];
    K --> L5[Compare SSL Certs];
    K --> L6[Correlate Public Findings];

    M[Identified Relationships & Recon Summary]
    L1 --> M; L2 --> M; L3 --> M; L4 --> M; L5 --> M; L6 --> M;

    M --> N[End of Initial Recon Phase];