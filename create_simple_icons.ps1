# Create simple icon files for the extension
# This script creates very basic colored squares as icons

Add-Type -AssemblyName System.Drawing

function Create-Icon {
    param (
        [int]$size,
        [string]$outputPath
    )
    
    $bitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Fill with Edge blue color
    $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 120, 215))
    $graphics.FillRectangle($brush, 0, 0, $size, $size)
    
    # Save the bitmap
    $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Clean up
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    
    Write-Host "Created icon: $outputPath" -ForegroundColor Green
}

# Create icons of different sizes
Create-Icon -size 16 -outputPath "icon16.png"
Create-Icon -size 48 -outputPath "icon48.png"
Create-Icon -size 128 -outputPath "icon128.png"

Write-Host "All icons created successfully." -ForegroundColor Cyan
