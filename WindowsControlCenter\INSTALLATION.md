# Windows Control Center - Installation Guide

## System Requirements

- Windows 10 or Windows 11
- .NET 6.0 Runtime or SDK (for building from source)
- 4GB RAM (minimum)
- 100MB free disk space
- Administrator privileges (for full functionality)

## Installation Options

### Option 1: Run from Source Code (Recommended for Developers)

1. **Install .NET 6.0 SDK**
   - Download from: https://dotnet.microsoft.com/download/dotnet/6.0
   - Follow the installation instructions for your system

2. **Clone or Download the Repository**
   - Download and extract the ZIP file
   - Or clone using Git: `git clone [repository-url]`

3. **Build and Run**
   - Navigate to the project directory
   - Run the `CompileAndRun.bat` script
   - Or manually build using: `dotnet build` and `dotnet run`

### Option 2: Run Pre-compiled Binary (For End Users)

1. **Install .NET 6.0 Runtime**
   - Download from: https://dotnet.microsoft.com/download/dotnet/6.0
   - Choose the ".NET Runtime" option (not the full SDK)
   - Follow the installation instructions for your system

2. **Download the Release Package**
   - Download the latest release ZIP file
   - Extract to a location of your choice

3. **Run the Application**
   - Double-click `WindowsControlCenter.exe`
   - For full functionality, right-click and select "Run as administrator"

## Verifying Installation

After launching the application, you should see:

1. The main window with multiple tabs
2. System information displaying correctly
3. Running applications listed in the first tab

If you encounter any issues:
- Check that .NET 6.0 is properly installed
- Verify you have sufficient permissions
- Try running as administrator

## Running with Administrator Privileges

Many features in Windows Control Center require administrator privileges:

### Method 1: Run as Administrator (Temporary)
1. Right-click on `WindowsControlCenter.exe`
2. Select "Run as administrator"
3. Confirm the UAC prompt

### Method 2: Set Administrator Mode Permanently
1. Right-click on `WindowsControlCenter.exe`
2. Select "Properties"
3. Go to the "Compatibility" tab
4. Check "Run this program as an administrator"
5. Click "Apply" and "OK"

## Uninstallation

Since Windows Control Center doesn't modify the registry or install system files, uninstallation is simple:

1. Close the application if it's running
2. Delete the application folder
3. Optionally, uninstall .NET 6.0 if no other applications require it

## Troubleshooting

### Error: "dotnet" is not recognized
- .NET SDK is not installed or not in your PATH
- Install .NET 6.0 SDK from the link above

### Error: Application crashes on startup
- Ensure you have the correct .NET version installed
- Try running as administrator
- Check Windows Event Viewer for detailed error information

### Error: Features not working
- Many features require administrator privileges
- Look for red warning text in the application
- Run the application as administrator

### Error: Icon not displaying correctly
- The icon creation script requires PowerShell with System.Drawing
- This is not critical for application functionality

## Getting Help

If you encounter issues not covered in this guide:

1. Check the `README.md` file for additional information
2. Review the `USER_GUIDE.md` for usage instructions
3. Submit an issue on the project repository
4. Contact the development team with details about your problem

Thank you for installing Windows Control Center!
