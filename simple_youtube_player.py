#!/usr/bin/env python3
"""
SimpleTube - A Simple YouTube Player with Ad Blocking
Built with PyQt6, yt-dlp, and QtMultimedia
"""

import os
import sys
import re
import json
import time
from urllib.parse import urlparse, parse_qs

import yt_dlp
import requests
from PyQt6.QtCore import Qt, QUrl, QSize, pyqtSignal, QThread, QTimer
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QLineEdit, QPushButton, QLabel, QSlider, QListWidget, 
                           QListWidgetItem, QSplitter, QProgressBar, QMessageBox, 
                           QToolBar, QStatusBar, QComboBox)
from PyQt6.QtGui import QIcon, QAction
from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput
from PyQt6.QtMultimediaWidgets import QVideoWidget

# Path for storing app data
APP_DATA_PATH = os.path.join(os.path.expanduser('~'), '.simpletube')
os.makedirs(APP_DATA_PATH, exist_ok=True)

# Default settings
DEFAULT_SETTINGS = {
    "video_quality": "best",
    "download_path": os.path.join(os.path.expanduser('~'), 'Videos'),
    "history_enabled": True,
    "autoplay": True,
    "volume": 70,
    "theme": "dark"
}

class VideoDownloader(QThread):
    """Thread for downloading video information using yt-dlp"""
    
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, url, quality="best"):
        super().__init__()
        self.url = url
        self.quality = quality
        
    def run(self):
        """Run the download thread"""
        try:
            self.progress_signal.emit("Fetching video information...")
            
            # Configure yt-dlp options
            ydl_opts = {
                'format': self.quality,
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': False,
                'noplaylist': True,
                'extract_flat': False,
                'skip_download': True,  # We're just getting info, not downloading
            }
            
            # Get video info
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                self.progress_signal.emit("Extracting video data...")
                info = ydl.extract_info(self.url, download=False)
                
                # Prepare result
                result = {
                    'title': info.get('title', 'Unknown Title'),
                    'url': self.url,
                    'formats': info.get('formats', []),
                    'thumbnail': info.get('thumbnail', None),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown Uploader'),
                    'view_count': info.get('view_count', 0),
                    'upload_date': info.get('upload_date', ''),
                    'description': info.get('description', ''),
                    'direct_url': None  # Will be set below
                }
                
                # Get the best format URL for direct playback
                for fmt in info.get('formats', []):
                    if fmt.get('format_id') == info.get('format_id'):
                        result['direct_url'] = fmt.get('url')
                        break
                
                if not result['direct_url'] and info.get('formats'):
                    # Fallback to any available format
                    result['direct_url'] = info.get('formats')[-1].get('url')
                
                self.finished_signal.emit(result)
                
        except Exception as e:
            self.error_signal.emit(f"Error: {str(e)}")


class SearchThread(QThread):
    """Thread for searching YouTube videos"""
    
    results_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    
    def __init__(self, query):
        super().__init__()
        self.query = query
        
    def run(self):
        """Run the search thread"""
        try:
            # Configure yt-dlp options for search
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': False,
                'extract_flat': True,
                'skip_download': True,
                'noplaylist': False,
            }
            
            # Search for videos
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Use YouTube search URL
                search_url = f"ytsearch10:{self.query}"
                info = ydl.extract_info(search_url, download=False)
                
                results = []
                for entry in info.get('entries', []):
                    if entry:
                        results.append({
                            'title': entry.get('title', 'Unknown Title'),
                            'url': entry.get('url', ''),
                            'thumbnail': entry.get('thumbnail', None),
                            'uploader': entry.get('uploader', 'Unknown Uploader'),
                            'duration': entry.get('duration', 0),
                        })
                
                self.results_signal.emit(results)
                
        except Exception as e:
            self.error_signal.emit(f"Error: {str(e)}")


class SimpleTubeApp(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SimpleTube - YouTube Player")
        self.setMinimumSize(1000, 600)
        
        # Load settings
        self.load_settings()
        
        # Initialize variables
        self.current_video = None
        self.search_results = []
        self.history = []
        self.load_history()
        
        # Set up UI
        self.setup_ui()
        
        # Set up timer for UI updates
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(500)  # Update every 500ms
        
    def load_settings(self):
        """Load application settings"""
        settings_path = os.path.join(APP_DATA_PATH, 'settings.json')
        try:
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    self.settings = json.load(f)
            else:
                self.settings = DEFAULT_SETTINGS
        except Exception as e:
            print(f"Error loading settings: {e}")
            self.settings = DEFAULT_SETTINGS
            
    def save_settings(self):
        """Save application settings"""
        settings_path = os.path.join(APP_DATA_PATH, 'settings.json')
        try:
            with open(settings_path, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")
            
    def load_history(self):
        """Load watch history"""
        if not self.settings["history_enabled"]:
            return
            
        history_path = os.path.join(APP_DATA_PATH, 'history.json')
        try:
            if os.path.exists(history_path):
                with open(history_path, 'r') as f:
                    self.history = json.load(f)
        except Exception as e:
            print(f"Error loading history: {e}")
            
    def save_history(self):
        """Save watch history"""
        if not self.settings["history_enabled"]:
            return
            
        history_path = os.path.join(APP_DATA_PATH, 'history.json')
        try:
            with open(history_path, 'w') as f:
                json.dump(self.history, f, indent=2)
        except Exception as e:
            print(f"Error saving history: {e}")
            
    def add_to_history(self, video_info):
        """Add video to watch history"""
        if not self.settings["history_enabled"]:
            return
            
        # Check if video already in history
        for i, item in enumerate(self.history):
            if item.get('url') == video_info.get('url'):
                # Move to top of history
                self.history.pop(i)
                break
                
        # Add to history (limit to 100 items)
        self.history.insert(0, {
            'title': video_info.get('title'),
            'url': video_info.get('url'),
            'thumbnail': video_info.get('thumbnail'),
            'timestamp': time.time()
        })
        
        if len(self.history) > 100:
            self.history = self.history[:100]
            
        self.save_history()
        
    def setup_ui(self):
        """Set up the user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create toolbar
        self.create_toolbar()
        
        # Create search bar
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search YouTube or enter URL...")
        self.search_input.returnPressed.connect(self.on_search)
        
        self.search_button = QPushButton("Search")
        self.search_button.clicked.connect(self.on_search)
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_button)
        
        main_layout.addLayout(search_layout)
        
        # Create splitter for video and sidebar
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Video area
        video_container = QWidget()
        video_layout = QVBoxLayout(video_container)
        
        # Video player
        self.video_widget = QVideoWidget()
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setVideoOutput(self.video_widget)
        self.media_player.setAudioOutput(self.audio_output)
        
        # Set initial volume
        self.audio_output.setVolume(self.settings["volume"] / 100.0)
        
        video_layout.addWidget(self.video_widget)
        
        # Video controls
        controls_layout = QHBoxLayout()
        
        self.play_button = QPushButton("Play")
        self.play_button.clicked.connect(self.toggle_playback)
        
        self.position_slider = QSlider(Qt.Orientation.Horizontal)
        self.position_slider.setRange(0, 1000)
        self.position_slider.sliderMoved.connect(self.seek_position)
        
        self.time_label = QLabel("00:00 / 00:00")
        
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(self.settings["volume"])
        self.volume_slider.valueChanged.connect(self.set_volume)
        
        controls_layout.addWidget(self.play_button)
        controls_layout.addWidget(self.position_slider)
        controls_layout.addWidget(self.time_label)
        controls_layout.addWidget(QLabel("Volume:"))
        controls_layout.addWidget(self.volume_slider)
        
        video_layout.addLayout(controls_layout)
        
        # Video info
        self.video_title = QLabel("No video loaded")
        self.video_title.setWordWrap(True)
        self.video_title.setStyleSheet("font-weight: bold; font-size: 14px;")
        
        self.video_info = QLabel("")
        self.video_info.setWordWrap(True)
        
        video_layout.addWidget(self.video_title)
        video_layout.addWidget(self.video_info)
        
        # Add video container to splitter
        splitter.addWidget(video_container)
        
        # Sidebar for search results and history
        sidebar = QWidget()
        sidebar_layout = QVBoxLayout(sidebar)
        
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self.on_result_clicked)
        
        sidebar_layout.addWidget(QLabel("Search Results:"))
        sidebar_layout.addWidget(self.results_list)
        
        # Add sidebar to splitter
        splitter.addWidget(sidebar)
        
        # Set initial splitter sizes
        splitter.setSizes([700, 300])
        
        main_layout.addWidget(splitter)
        
        # Status bar
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        
        # Progress bar in status bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setMaximumHeight(15)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.progress_bar.hide()
        
        self.statusBar.addPermanentWidget(self.progress_bar)
        
        # Connect media player signals
        self.media_player.durationChanged.connect(self.duration_changed)
        self.media_player.positionChanged.connect(self.position_changed)
        self.media_player.playbackStateChanged.connect(self.playback_state_changed)
        
    def create_toolbar(self):
        """Create the application toolbar"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # Home action
        home_action = QAction("Home", self)
        home_action.triggered.connect(self.show_home)
        toolbar.addAction(home_action)
        
        # History action
        history_action = QAction("History", self)
        history_action.triggered.connect(self.show_history)
        toolbar.addAction(history_action)
        
        # Settings action
        settings_action = QAction("Settings", self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        # Quality selector
        toolbar.addSeparator()
        toolbar.addWidget(QLabel("Quality:"))
        
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["best", "1080p", "720p", "480p", "360p"])
        self.quality_combo.setCurrentText(self.settings["video_quality"])
        self.quality_combo.currentTextChanged.connect(self.change_quality)
        toolbar.addWidget(self.quality_combo)
        
    def on_search(self):
        """Handle search action"""
        query = self.search_input.text().strip()
        if not query:
            return
            
        # Check if it's a URL
        if re.match(r'https?://', query):
            self.load_video(query)
        else:
            # It's a search query
            self.search_videos(query)
            
    def search_videos(self, query):
        """Search for videos"""
        self.statusBar.showMessage(f"Searching for: {query}")
        self.progress_bar.show()
        
        # Clear previous results
        self.results_list.clear()
        
        # Start search thread
        self.search_thread = SearchThread(query)
        self.search_thread.results_signal.connect(self.on_search_results)
        self.search_thread.error_signal.connect(self.on_search_error)
        self.search_thread.start()
        
    def on_search_results(self, results):
        """Handle search results"""
        self.search_results = results
        self.results_list.clear()
        
        for result in results:
            item = QListWidgetItem(result['title'])
            item.setData(Qt.ItemDataRole.UserRole, result)
            self.results_list.addItem(item)
            
        self.statusBar.showMessage(f"Found {len(results)} videos")
        self.progress_bar.hide()
        
    def on_search_error(self, error):
        """Handle search error"""
        self.statusBar.showMessage(error)
        self.progress_bar.hide()
        QMessageBox.warning(self, "Search Error", error)
        
    def on_result_clicked(self, item):
        """Handle result item click"""
        result = item.data(Qt.ItemDataRole.UserRole)
        if result and 'url' in result:
            self.load_video(result['url'])
            
    def load_video(self, url):
        """Load a video from URL"""
        self.statusBar.showMessage(f"Loading video: {url}")
        self.progress_bar.show()
        
        # Start download thread
        self.download_thread = VideoDownloader(url, self.settings["video_quality"])
        self.download_thread.progress_signal.connect(self.statusBar.showMessage)
        self.download_thread.finished_signal.connect(self.on_video_info)
        self.download_thread.error_signal.connect(self.on_video_error)
        self.download_thread.start()
        
    def on_video_info(self, video_info):
        """Handle video information"""
        self.current_video = video_info
        
        # Update UI
        self.video_title.setText(video_info['title'])
        
        info_text = f"Uploader: {video_info['uploader']}\n"
        info_text += f"Views: {video_info['view_count']:,}\n"
        
        if video_info['upload_date']:
            date = video_info['upload_date']
            formatted_date = f"{date[0:4]}-{date[4:6]}-{date[6:8]}"
            info_text += f"Upload Date: {formatted_date}\n"
            
        self.video_info.setText(info_text)
        
        # Play video
        if video_info['direct_url']:
            self.media_player.setSource(QUrl(video_info['direct_url']))
            self.media_player.play()
            
            # Add to history
            self.add_to_history(video_info)
            
        self.statusBar.showMessage(f"Playing: {video_info['title']}")
        self.progress_bar.hide()
        
    def on_video_error(self, error):
        """Handle video error"""
        self.statusBar.showMessage(error)
        self.progress_bar.hide()
        QMessageBox.warning(self, "Video Error", error)
        
    def toggle_playback(self):
        """Toggle play/pause"""
        if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()
            
    def seek_position(self):
        """Seek to position based on slider"""
        if self.media_player.duration() > 0:
            position = self.position_slider.value() / 1000.0 * self.media_player.duration()
            self.media_player.setPosition(int(position))
            
    def set_volume(self, volume):
        """Set volume from slider"""
        self.audio_output.setVolume(volume / 100.0)
        self.settings["volume"] = volume
        
    def duration_changed(self, duration):
        """Handle duration change"""
        self.position_slider.setRange(0, duration)
        
    def position_changed(self, position):
        """Handle position change"""
        if self.media_player.duration() > 0:
            # Update position slider
            position_percent = position / self.media_player.duration()
            self.position_slider.setValue(int(position_percent * 1000))
            
            # Update time label
            position_str = self.format_time(position / 1000)
            duration_str = self.format_time(self.media_player.duration() / 1000)
            self.time_label.setText(f"{position_str} / {duration_str}")
            
    def playback_state_changed(self, state):
        """Handle playback state change"""
        if state == QMediaPlayer.PlaybackState.PlayingState:
            self.play_button.setText("Pause")
        else:
            self.play_button.setText("Play")
            
    def update_ui(self):
        """Update UI elements based on playback state"""
        # This method is called by the timer
        # Most UI updates are handled by signal connections
        pass
            
    def format_time(self, seconds):
        """Format seconds as mm:ss"""
        if seconds is None:
            return "00:00"
            
        m, s = divmod(int(seconds), 60)
        h, m = divmod(m, 60)
        
        if h > 0:
            return f"{h:02d}:{m:02d}:{s:02d}"
        else:
            return f"{m:02d}:{s:02d}"
            
    def show_home(self):
        """Show home screen"""
        # For now, just clear the results
        self.results_list.clear()
        self.statusBar.showMessage("Home")
        
    def show_history(self):
        """Show watch history"""
        self.results_list.clear()
        
        for item in self.history:
            list_item = QListWidgetItem(item['title'])
            list_item.setData(Qt.ItemDataRole.UserRole, item)
            self.results_list.addItem(list_item)
            
        self.statusBar.showMessage("Watch History")
        
    def show_settings(self):
        """Show settings dialog"""
        # For now, just show a message
        QMessageBox.information(self, "Settings", "Settings dialog would appear here")
        
    def change_quality(self, quality):
        """Change video quality"""
        self.settings["video_quality"] = quality
        self.statusBar.showMessage(f"Quality set to: {quality}")
        
        # If a video is currently playing, reload it with the new quality
        if self.current_video:
            self.load_video(self.current_video['url'])
        
    def closeEvent(self, event):
        """Handle window close event"""
        self.save_settings()
        self.save_history()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setApplicationName("SimpleTube")
    app.setOrganizationName("SimpleTube")
    
    window = SimpleTubeApp()
    window.show()
    
    sys.exit(app.exec())
