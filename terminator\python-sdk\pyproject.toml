[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "desktop-use"
version = "0.1.3"
authors = [
  { name="mediar-ai", email="<EMAIL>" },
]
description = "Python SDK for the Terminator desktop automation server"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License", # Choose your license
    "Operating System :: OS Independent",
]
dependencies = [
    "requests>=2.20",
]

[project.urls]
"Homepage" = "https://github.com/mediar-ai/terminator" # Replace with actual URL
"Bug Tracker" = "https://github.com/mediar-ai/terminator/issues" # Replace with actual URL 