# PowerShell script to open browser, navigate to DeepSeek, and chat about cybersecurity

# Add reference to Windows Forms for SendKeys
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;
using System.Threading;

public class BrowserAutomation
{
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    [DllImport("user32.dll")]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
    
    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);
    
    public const int KEYEVENTF_EXTENDEDKEY = 0x0001;
    public const int KEYEVENTF_KEYUP = 0x0002;
    
    public const byte VK_RETURN = 0x0D; // Enter key
    
    public static void SendEnter()
    {
        // Press Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static bool ActivateBrowserWindow(string partialTitle)
    {
        // Try to find and activate browser window with the given partial title
        IntPtr hWnd = FindWindow(null, partialTitle);
        if (hWnd != IntPtr.Zero)
        {
            return SetForegroundWindow(hWnd);
        }
        return false;
    }
}
"@

# Function to wait for a specified time
function Wait-Time {
    param (
        [int]$Milliseconds
    )
    
    Start-Sleep -Milliseconds $Milliseconds
}

# Function to take a screenshot
function Take-Screenshot {
    param (
        [string]$FilePath
    )
    
    $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
    $bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.Size)
    $bitmap.Save($FilePath)
    $graphics.Dispose()
    $bitmap.Dispose()
    
    Write-Host "Screenshot saved to $FilePath"
}

# Step 1: Open the default browser with DeepSeek
Write-Host "Opening browser and navigating to DeepSeek..."
Start-Process "https://chat.deepseek.com/"

# Wait for the browser to open and load the page
Write-Host "Waiting for the page to load..."
Wait-Time 8000

# Take a screenshot to see if the page loaded
Take-Screenshot -FilePath "terminator-test\deepseek_loaded.png"

# Step 2: Try to find and activate the browser window
$browserActivated = $false
$browserTitles = @("DeepSeek", "Chat - DeepSeek", "DeepSeek Chat", "Chrome", "Edge", "Firefox")

foreach ($title in $browserTitles) {
    Write-Host "Trying to activate browser window with title containing '$title'..."
    $browserActivated = [BrowserAutomation]::ActivateBrowserWindow($title)
    if ($browserActivated) {
        Write-Host "Successfully activated browser window with title containing '$title'!"
        break
    }
}

if (-not $browserActivated) {
    Write-Host "Could not find browser window. Assuming it's already in focus."
}

# Wait a moment to ensure the page is fully loaded
Wait-Time 2000

# Take another screenshot
Take-Screenshot -FilePath "terminator-test\deepseek_activated.png"

# Step 3: Start chatting about cybersecurity
$messages = @(
    "Hello! I'd like to discuss a cybersecurity project I'm working on.",
    "I'm looking to develop a comprehensive security assessment framework for a medium-sized organization. Can you help me outline the key components?",
    "What are the most critical security controls I should implement first?",
    "Can you suggest some open-source tools for vulnerability scanning and penetration testing?"
)

# Send each message and wait for a response
foreach ($message in $messages) {
    Write-Host "Sending message: $message"
    
    # Type the message
    [System.Windows.Forms.SendKeys]::SendWait($message)
    Wait-Time 1000
    
    # Take a screenshot before sending
    Take-Screenshot -FilePath "terminator-test\deepseek_before_send_$($messages.IndexOf($message)).png"
    
    # Send Enter to submit the message
    [BrowserAutomation]::SendEnter()
    
    # Wait for DeepSeek to respond
    Write-Host "Waiting for DeepSeek to respond..."
    $waitTime = 10000  # Longer wait time for more complex responses
    Wait-Time $waitTime
    
    # Take a screenshot after response
    Take-Screenshot -FilePath "terminator-test\deepseek_after_response_$($messages.IndexOf($message)).png"
}

# Take a final screenshot
Take-Screenshot -FilePath "terminator-test\deepseek_final_chat.png"

Write-Host "Chat with DeepSeek completed!"
