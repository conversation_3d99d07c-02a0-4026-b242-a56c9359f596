# OpenRouter Integration for DeepSeek Terminal

This document explains how the DeepSeek Terminal client has been modified to work with OpenRouter instead of connecting directly to the DeepSeek API.

## What is OpenRouter?

OpenRouter is a unified API that provides access to multiple AI models, including DeepSeek models, through a single endpoint. It offers several advantages:

- Access to multiple AI models with a single API key
- Automatic fallbacks if a model is unavailable
- Cost optimization by selecting the most efficient provider
- Simplified integration with consistent API format

## Changes Made to DeepSeek Terminal

The following changes have been made to enable OpenRouter integration:

1. **API Endpoint**: Changed from DeepSeek's direct API to OpenRouter's endpoint
   ```powershell
   $apiEndpoint = "https://openrouter.ai/api/v1/chat/completions"
   ```

2. **Model Identifiers**: Updated to use OpenRouter's model naming convention
   ```powershell
   $defaultModel = "deepseek/deepseek-chat"  # DeepSeek V3
   # Alternative: "deepseek/deepseek-r1"     # DeepSeek R1
   ```

3. **API Headers**: Added OpenRouter-specific headers for analytics
   ```powershell
   $headers = @{
       "Authorization" = "Bearer $apiKey"
       "Content-Type" = "application/json"
       "HTTP-Referer" = "DeepSeekTerminal"
       "X-Title" = "DeepSeek Terminal Client"
   }
   ```

4. **Model Selection**: Added ability to choose between DeepSeek models
   - DeepSeek V3 (deepseek/deepseek-chat)
   - DeepSeek R1 (deepseek/deepseek-r1)

5. **Error Handling**: Enhanced error messages for OpenRouter-specific errors
   - Authentication issues
   - Rate limiting
   - Payment requirements
   - Model availability

## How to Get an OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/) and create an account
2. Go to [API Keys](https://openrouter.ai/keys) to generate a new key
3. Copy your API key and use it in the DeepSeek Terminal client

## Using DeepSeek Models via OpenRouter

When you start a new chat, you'll be prompted to select a model:
- **DeepSeek V3** (deepseek/deepseek-chat): The latest model from DeepSeek, good for general use
- **DeepSeek R1** (deepseek/deepseek-r1): A reasoning-focused model, better for complex tasks

## Troubleshooting

### Authentication Errors
- Verify that your OpenRouter API key is correct
- Check that your OpenRouter account is active
- Ensure you have sufficient credits in your OpenRouter account

### Model Not Available
- The selected model might be temporarily unavailable
- Try switching to a different model
- Check the OpenRouter status page for any outages

### Payment Required
- You may need to add credits to your OpenRouter account
- Visit [OpenRouter Credits](https://openrouter.ai/settings/credits) to add funds

## Additional Resources

- [OpenRouter Documentation](https://openrouter.ai/docs)
- [OpenRouter Models](https://openrouter.ai/models)
- [OpenRouter API Reference](https://openrouter.ai/docs/api-reference/overview)
