#NoEnv  ; Recommended for performance and compatibility with future AutoHotkey releases.
#SingleInstance force  ; Ensures only one instance of this script is running
SendMode Input  ; Recommended for new scripts due to its superior speed and reliability.
SetWorkingDir %A_ScriptDir%  ; Ensures a consistent starting directory.

; ========== CONFIGURATION SECTION ==========
; You can modify these variables to customize the automation

; Delay between actions in milliseconds (adjust as needed)
global actionDelay := 500

; Number of repetitions for the task
global repetitions := 5

; Target window title (partial match is fine)
global targetWindow := "Windows"

; ========== AUTOMATION ACTIONS ==========
; This array contains the sequence of actions to perform
; Each action is an object with:
; - type: The type of action (click, type, hotkey, wait)
; - params: Parameters specific to the action type

global actions := []

; Example actions (modify these for your specific task)
actions.Push({type: "activate", params: {window: targetWindow}})
actions.Push({type: "wait", params: {time: 1000}})
actions.Push({type: "click", params: {x: 500, y: 500}})
actions.Push({type: "wait", params: {time: 500}})
actions.Push({type: "type", params: {text: "Hello World"}})
actions.Push({type: "wait", params: {time: 500}})
actions.Push({type: "hotkey", params: {keys: "^a"}})  ; Ctrl+A
actions.Push({type: "wait", params: {time: 500}})
actions.Push({type: "hotkey", params: {keys: "{Delete}"}})  ; Delete key

; ========== AUTOMATION FUNCTIONS ==========

; Perform a single action based on its type
PerformAction(action) {
    if (action.type = "activate") {
        WinActivate, % action.params.window
        Sleep, actionDelay
    } else if (action.type = "click") {
        Click, % action.params.x " " action.params.y
        Sleep, actionDelay
    } else if (action.type = "type") {
        SendInput, % action.params.text
        Sleep, actionDelay
    } else if (action.type = "hotkey") {
        SendInput, % action.params.keys
        Sleep, actionDelay
    } else if (action.type = "wait") {
        Sleep, % action.params.time
    }
}

; Run the complete automation sequence
RunAutomation() {
    for i, action in actions {
        PerformAction(action)
    }
}

; Run the automation multiple times
RunMultipleAutomations() {
    for i in range(1, repetitions) {
        RunAutomation()
        Sleep, 1000  ; Wait between repetitions
    }
    MsgBox, Automation completed!
}

; Helper function to create a range
range(start, end) {
    static range := {}
    range.length := end - start + 1
    range._start := start
    range._end := end
    
    ; Define __Enum method
    range.__Enum := Func("_RangeEnum")
    return range
}

; Helper function for range enumeration
_RangeEnum(range, &i, &v="") {
    if (i = "") {  ; Init
        i := range._start
        v := i
        return i <= range._end
    }
    
    if (i < range._end) {
        i += 1
        v := i
        return true
    }
    
    return false
}

; ========== HOTKEYS ==========

; F8: Run the automation once
F8::
    RunAutomation()
    return

; F9: Run the automation multiple times
F9::
    RunMultipleAutomations()
    return

; F10: Reload the script
F10::
    Reload
    return

; F12: Exit the script
F12::
    ExitApp
    return

; ========== MOUSE POSITION TRACKER ==========
; Press Ctrl+Alt+P to get the current mouse position

^!p::
    MouseGetPos, xpos, ypos
    Clipboard := "x: " xpos ", y: " ypos
    TrayTip, Mouse Position, Position copied to clipboard: x: %xpos%, y: %ypos%, 2
    return

; ========== INITIALIZATION ==========
; Show initial instructions
MsgBox, 0, Task Automation, Script is ready!`n`n- Press F8 to run the automation once`n- Press F9 to run the automation %repetitions% times`n- Press F10 to reload the script`n- Press F12 to exit the script`n`n- Press Ctrl+Alt+P to get the current mouse position
