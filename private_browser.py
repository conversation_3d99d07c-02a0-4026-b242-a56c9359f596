#!/usr/bin/env python3
"""
PrivateFox - A Private, Fast, and Reliable Web Browser
Built with PyQt6 and QtWebEngine
"""

import os
import sys
import json
from datetime import datetime
from PyQt6.QtCore import QUrl, Qt, QSize, pyqtSignal, QTimer
from PyQt6.QtWidgets import (QApplication, QMainWindow, QTabWidget, QToolBar,
                           QLineEdit, QPushButton, QProgressBar, QStatusBar,
                           QMenu, QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QCheckBox, QComboBox, QFileDialog, QMessageBox, QWidget)
from PyQt6.QtGui import QIcon, QAction, QKeySequence
from PyQt6.QtWebEngineCore import QWebEngineProfile, QWebEnginePage, QWebEngineUrlRequestInterceptor
from PyQt6.QtWebEngineWidgets import QWebEngineView

# Path for storing browser data
APP_DATA_PATH = os.path.join(os.path.expanduser('~'), '.privatefox')
os.makedirs(APP_DATA_PATH, exist_ok=True)

# Ad and tracker blocking lists
ADBLOCK_LISTS = [
    "https://easylist.to/easylist/easylist.txt",
    "https://easylist.to/easylist/easyprivacy.txt",
    "https://secure.fanboy.co.nz/fanboy-annoyance.txt"
]

# Default settings
DEFAULT_SETTINGS = {
    "enable_adblock": True,
    "enable_tracking_protection": True,
    "enable_https_everywhere": True,
    "clear_on_exit": True,
    "custom_user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "search_engine": "https://duckduckgo.com/?q={}",
    "homepage": "https://duckduckgo.com",
    "enable_javascript": True,
    "block_popups": True,
    "enable_do_not_track": True
}

class UrlRequestInterceptor(QWebEngineUrlRequestInterceptor):
    """Intercepts URL requests to block ads and trackers"""

    def __init__(self, ad_block_rules=None, parent=None):
        super().__init__(parent)
        self.ad_block_rules = ad_block_rules or []
        self.https_redirect = True
        self.load_rules()

    def load_rules(self):
        """Load ad blocking rules from files"""
        # In a real implementation, this would parse the adblock lists
        # For simplicity, we'll use a small subset of common ad domains
        self.ad_domains = [
            "googleadservices.com", "doubleclick.net", "adservice.google.com",
            "advertising.com", "adnxs.com", "scorecardresearch.com", "facebook.net",
            "google-analytics.com", "googletagmanager.com", "amazon-adsystem.com"
        ]

    def interceptRequest(self, info):
        url = info.requestUrl().toString()

        # Block ads and trackers
        if any(ad_domain in url for ad_domain in self.ad_domains):
            info.block(True)
            return

        # Enforce HTTPS
        if self.https_redirect and url.startswith("http:"):
            https_url = url.replace("http:", "https:", 1)
            info.redirect(QUrl(https_url))

        # Set Do Not Track header
        info.setHttpHeader(b"DNT", b"1")


class WebEnginePage(QWebEnginePage):
    """Custom WebEnginePage with enhanced privacy features"""

    def __init__(self, profile, parent=None):
        super().__init__(profile, parent)

    def certificateError(self, error):
        # Always reject invalid certificates for security
        return False

    def javaScriptConfirm(self, url, msg):
        # Block JavaScript confirmations in private mode
        if self.profile().isOffTheRecord():
            return False
        return super().javaScriptConfirm(url, msg)

    def javaScriptPrompt(self, url, msg, default):
        # Block JavaScript prompts in private mode
        if self.profile().isOffTheRecord():
            return False, ""
        return super().javaScriptPrompt(url, msg, default)


class WebEngineView(QWebEngineView):
    """Custom WebEngineView with enhanced features"""

    new_tab_signal = pyqtSignal(QUrl)

    def __init__(self, profile, parent=None):
        super().__init__(parent)
        self.page = WebEnginePage(profile, self)
        self.setPage(self.page)

    def createWindow(self, window_type):
        # Handle new window/tab requests
        new_view = WebEngineView(self.page.profile(), self)
        new_view.new_tab_signal.connect(self.new_tab_signal)
        return new_view

    def contextMenuEvent(self, event):
        # Create custom context menu
        menu = self.page().createStandardContextMenu()
        menu.addSeparator()

        # Add "Open in New Tab" action
        if self.page().contextMenuData().linkUrl().isValid():
            open_in_new_tab = QAction("Open in New Tab", self)
            open_in_new_tab.triggered.connect(
                lambda: self.new_tab_signal.emit(self.page().contextMenuData().linkUrl())
            )
            menu.addAction(open_in_new_tab)

        menu.exec(event.globalPos())


class BrowserTab(QWidget):
    """A browser tab containing a WebEngineView"""

    def __init__(self, profile, parent=None):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)

        # Create web view
        self.web_view = WebEngineView(profile, self)
        self.layout.addWidget(self.web_view)

        # Set up progress bar
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setMaximumHeight(2)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("QProgressBar {background-color: transparent; border: 0px;} QProgressBar::chunk {background-color: #0078D7;}")
        self.layout.addWidget(self.progress_bar)

        # Connect signals
        self.web_view.loadProgress.connect(self.progress_bar.setValue)
        self.web_view.loadFinished.connect(self.on_load_finished)

    def on_load_finished(self, ok):
        self.progress_bar.hide()

    def load(self, url):
        self.progress_bar.show()
        self.web_view.load(url)


class PrivateBrowser(QMainWindow):
    """Main browser window"""

    def __init__(self):
        super().__init__()
        self.load_settings()
        self.setup_browser()

    def load_settings(self):
        """Load browser settings"""
        settings_path = os.path.join(APP_DATA_PATH, 'settings.json')
        try:
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    self.settings = json.load(f)
            else:
                self.settings = DEFAULT_SETTINGS
        except Exception as e:
            print(f"Error loading settings: {e}")
            self.settings = DEFAULT_SETTINGS

    def save_settings(self):
        """Save browser settings"""
        settings_path = os.path.join(APP_DATA_PATH, 'settings.json')
        try:
            with open(settings_path, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")

    def setup_browser(self):
        """Set up the browser UI and components"""
        self.setWindowTitle("PrivateFox - Private Browser")
        self.setGeometry(100, 100, 1200, 800)

        # Create private web profile
        self.profile = QWebEngineProfile("PrivateFox", self)
        self.profile.setHttpUserAgent(self.settings["custom_user_agent"])
        self.profile.setHttpCacheType(QWebEngineProfile.HttpCacheType.MemoryHttpCache)
        self.profile.setPersistentCookiesPolicy(QWebEngineProfile.PersistentCookiesPolicy.NoPersistentCookies)

        # Set up URL interceptor for ad blocking
        self.interceptor = UrlRequestInterceptor(parent=self)
        self.profile.setUrlRequestInterceptor(self.interceptor)

        # Create UI components
        self.create_tab_widget()
        self.create_toolbar()
        self.create_statusbar()
        self.create_menus()

        # Load homepage
        self.add_new_tab(QUrl(self.settings["homepage"]))

        # Set up auto-clear timer if enabled
        if self.settings["clear_on_exit"]:
            self.clear_timer = QTimer(self)
            self.clear_timer.timeout.connect(self.clear_browsing_data)
            self.clear_timer.start(30 * 60 * 1000)  # Clear every 30 minutes

    def create_tab_widget(self):
        """Create the tab widget for browser tabs"""
        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.setMovable(True)
        self.tabs.tabCloseRequested.connect(self.close_tab)
        self.tabs.currentChanged.connect(self.tab_changed)

        # Add "New Tab" button
        self.tabs.setCornerWidget(self.create_new_tab_button())

        self.setCentralWidget(self.tabs)

    def create_new_tab_button(self):
        """Create a new tab button for the tab widget"""
        new_tab_button = QPushButton("+")
        new_tab_button.setToolTip("New Tab")
        new_tab_button.setMaximumSize(QSize(24, 24))
        new_tab_button.clicked.connect(lambda: self.add_new_tab(QUrl(self.settings["homepage"])))
        return new_tab_button

    def create_toolbar(self):
        """Create the browser toolbar"""
        self.toolbar = QToolBar("Navigation")
        self.toolbar.setMovable(False)
        self.toolbar.setIconSize(QSize(16, 16))
        self.addToolBar(self.toolbar)

        # Back button
        self.back_button = QAction(QIcon.fromTheme("go-previous"), "Back", self)
        self.back_button.setShortcut(QKeySequence.StandardKey.Back)
        self.back_button.triggered.connect(lambda: self.tabs.currentWidget().web_view.back())
        self.toolbar.addAction(self.back_button)

        # Forward button
        self.forward_button = QAction(QIcon.fromTheme("go-next"), "Forward", self)
        self.forward_button.setShortcut(QKeySequence.StandardKey.Forward)
        self.forward_button.triggered.connect(lambda: self.tabs.currentWidget().web_view.forward())
        self.toolbar.addAction(self.forward_button)

        # Reload button
        self.reload_button = QAction(QIcon.fromTheme("view-refresh"), "Reload", self)
        self.reload_button.setShortcut(QKeySequence.StandardKey.Refresh)
        self.reload_button.triggered.connect(lambda: self.tabs.currentWidget().web_view.reload())
        self.toolbar.addAction(self.reload_button)

        # Home button
        self.home_button = QAction(QIcon.fromTheme("go-home"), "Home", self)
        self.home_button.triggered.connect(self.navigate_home)
        self.toolbar.addAction(self.home_button)

        # URL bar
        self.url_bar = QLineEdit()
        self.url_bar.returnPressed.connect(self.navigate_to_url)
        self.toolbar.addWidget(self.url_bar)

        # Settings button
        self.settings_button = QAction(QIcon.fromTheme("preferences-system"), "Settings", self)
        self.settings_button.triggered.connect(self.show_settings)
        self.toolbar.addAction(self.settings_button)

    def create_statusbar(self):
        """Create the status bar"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)

        # Add privacy indicator
        self.privacy_indicator = QLabel("🔒 Private Browsing")
        self.statusbar.addPermanentWidget(self.privacy_indicator)

    def create_menus(self):
        """Create the browser menus"""
        # File menu
        file_menu = self.menuBar().addMenu("&File")

        new_tab_action = QAction("New Tab", self)
        new_tab_action.setShortcut(QKeySequence.StandardKey.AddTab)
        new_tab_action.triggered.connect(lambda: self.add_new_tab(QUrl(self.settings["homepage"])))
        file_menu.addAction(new_tab_action)

        close_tab_action = QAction("Close Tab", self)
        close_tab_action.setShortcut(QKeySequence.StandardKey.Close)
        close_tab_action.triggered.connect(lambda: self.close_tab(self.tabs.currentIndex()))
        file_menu.addAction(close_tab_action)

        file_menu.addSeparator()

        exit_action = QAction("Exit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Edit menu
        edit_menu = self.menuBar().addMenu("&Edit")

        find_action = QAction("Find", self)
        find_action.setShortcut(QKeySequence.StandardKey.Find)
        # find_action.triggered.connect(self.show_find_dialog)
        edit_menu.addAction(find_action)

        # View menu
        view_menu = self.menuBar().addMenu("&View")

        zoom_in_action = QAction("Zoom In", self)
        zoom_in_action.setShortcut(QKeySequence.StandardKey.ZoomIn)
        zoom_in_action.triggered.connect(lambda: self.tabs.currentWidget().web_view.setZoomFactor(
            self.tabs.currentWidget().web_view.zoomFactor() + 0.1))
        view_menu.addAction(zoom_in_action)

        zoom_out_action = QAction("Zoom Out", self)
        zoom_out_action.setShortcut(QKeySequence.StandardKey.ZoomOut)
        zoom_out_action.triggered.connect(lambda: self.tabs.currentWidget().web_view.setZoomFactor(
            self.tabs.currentWidget().web_view.zoomFactor() - 0.1))
        view_menu.addAction(zoom_out_action)

        reset_zoom_action = QAction("Reset Zoom", self)
        reset_zoom_action.triggered.connect(lambda: self.tabs.currentWidget().web_view.setZoomFactor(1.0))
        view_menu.addAction(reset_zoom_action)

        # Tools menu
        tools_menu = self.menuBar().addMenu("&Tools")

        clear_data_action = QAction("Clear Browsing Data", self)
        clear_data_action.triggered.connect(self.clear_browsing_data)
        tools_menu.addAction(clear_data_action)

        # Help menu
        help_menu = self.menuBar().addMenu("&Help")

        about_action = QAction("About PrivateFox", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def add_new_tab(self, url=None, title="New Tab"):
        """Add a new browser tab"""
        if url is None:
            url = QUrl(self.settings["homepage"])

        # Create new tab
        browser_tab = BrowserTab(self.profile, self)
        browser_tab.web_view.new_tab_signal.connect(self.add_new_tab)
        browser_tab.web_view.titleChanged.connect(
            lambda title, tab=browser_tab: self.update_tab_title(tab, title))
        browser_tab.web_view.urlChanged.connect(
            lambda url, tab=browser_tab: self.update_url_bar(url))

        # Add tab to widget
        index = self.tabs.addTab(browser_tab, title)
        self.tabs.setCurrentIndex(index)

        # Load URL
        browser_tab.load(url)
        return browser_tab

    def close_tab(self, index):
        """Close a browser tab"""
        if self.tabs.count() > 1:
            self.tabs.removeTab(index)
        else:
            # Don't close the last tab, just clear it
            self.tabs.widget(0).load(QUrl(self.settings["homepage"]))

    def tab_changed(self, index):
        """Handle tab change event"""
        if index >= 0:
            current_tab = self.tabs.widget(index)
            self.update_url_bar(current_tab.web_view.url())

    def update_tab_title(self, tab, title):
        """Update the title of a tab"""
        index = self.tabs.indexOf(tab)
        if index >= 0:
            self.tabs.setTabText(index, title[:20] + "..." if len(title) > 20 else title)

    def update_url_bar(self, url):
        """Update the URL bar with the current URL"""
        self.url_bar.setText(url.toString())
        self.url_bar.setCursorPosition(0)

    def navigate_to_url(self):
        """Navigate to the URL in the URL bar"""
        url_text = self.url_bar.text().strip()

        # Check if it's a search query or URL
        if " " in url_text or not ("." in url_text or url_text.startswith("http")):
            # It's a search query
            search_url = self.settings["search_engine"].format(url_text)
            self.tabs.currentWidget().load(QUrl(search_url))
        else:
            # It's a URL
            if not url_text.startswith(("http://", "https://")):
                url_text = "https://" + url_text
            self.tabs.currentWidget().load(QUrl(url_text))

    def navigate_home(self):
        """Navigate to the homepage"""
        self.tabs.currentWidget().load(QUrl(self.settings["homepage"]))

    def clear_browsing_data(self):
        """Clear browsing data"""
        self.profile.clearHttpCache()
        self.profile.clearAllVisitedLinks()
        self.statusbar.showMessage("Browsing data cleared", 3000)

    def show_settings(self):
        """Show the settings dialog"""
        # This would be implemented in a real browser
        QMessageBox.information(self, "Settings", "Settings dialog would appear here")

    def show_about(self):
        """Show the about dialog"""
        QMessageBox.about(self, "About PrivateFox",
                         "PrivateFox - A Private, Fast, and Reliable Web Browser\n\n"
                         "Built with PyQt6 and QtWebEngine\n\n"
                         "Features:\n"
                         "- Built-in ad blocking\n"
                         "- Tracker blocking\n"
                         "- Private browsing mode\n"
                         "- History/cookie auto-deletion\n"
                         "- Custom user agent\n"
                         "- HTTPS enforcement")

    def closeEvent(self, event):
        """Handle window close event"""
        if self.settings["clear_on_exit"]:
            self.clear_browsing_data()
        self.save_settings()
        event.accept()


if __name__ == "__main__":
    print("Starting PrivateFox Browser...")
    app = QApplication(sys.argv)
    app.setApplicationName("PrivateFox")
    app.setOrganizationName("PrivateFox")

    print("Creating browser instance...")
    browser = PrivateBrowser()
    print("Showing browser window...")
    browser.show()

    print("Entering application main loop...")
    sys.exit(app.exec())
