# Script to get Microsoft Edge tabs using UI Automation
Add-Type -AssemblyName UIAutomationClient
Add-Type -AssemblyName System.Windows.Forms

Write-Host "Searching for Microsoft Edge windows..." -ForegroundColor Cyan

# Find all Edge windows
$edgeProcesses = Get-Process msedge | Where-Object { $_.MainWindowHandle -ne 0 }

if ($edgeProcesses.Count -eq 0) {
    Write-Host "No Microsoft Edge windows found with active UI." -ForegroundColor Yellow
    exit
}

Write-Host "Found $($edgeProcesses.Count) Microsoft Edge window(s) with active UI." -ForegroundColor Green

# Function to get the current URL using keyboard shortcuts
function Get-CurrentUrl {
    # Clear clipboard
    [System.Windows.Forms.Clipboard]::Clear()
    
    # Send Ctrl+L to select address bar
    $wshell = New-Object -ComObject wscript.shell
    $wshell.SendKeys("^l")
    Start-Sleep -Milliseconds 300
    
    # Send Ctrl+C to copy
    $wshell.SendKeys("^c")
    Start-Sleep -Milliseconds 300
    
    # Get text from clipboard
    $url = [System.Windows.Forms.Clipboard]::GetText()
    return $url
}

# Function to get tab information using UI Automation
function Get-EdgeTabs {
    param (
        [System.Diagnostics.Process]$EdgeProcess
    )
    
    try {
        # Get the automation element for the Edge window
        $element = [System.Windows.Automation.AutomationElement]::FromHandle($EdgeProcess.MainWindowHandle)
        
        if ($element -eq $null) {
            Write-Host "Could not get automation element for Edge window." -ForegroundColor Red
            return
        }
        
        # Try to activate the window
        $wshell = New-Object -ComObject wscript.shell
        $result = $wshell.AppActivate($EdgeProcess.Id)
        
        if (-not $result) {
            Write-Host "Could not activate Edge window." -ForegroundColor Red
            return
        }
        
        Write-Host "Window Title: $($EdgeProcess.MainWindowTitle)" -ForegroundColor Cyan
        
        # Try to find tab elements
        $tabsCondition = New-Object System.Windows.Automation.PropertyCondition(
            [System.Windows.Automation.AutomationElement]::ControlTypeProperty, 
            [System.Windows.Automation.ControlType]::TabItem
        )
        
        $tabs = $element.FindAll(
            [System.Windows.Automation.TreeScope]::Descendants, 
            $tabsCondition
        )
        
        if ($tabs -ne $null -and $tabs.Count -gt 0) {
            Write-Host "Found $($tabs.Count) tabs." -ForegroundColor Green
            
            for ($i = 0; $i -lt $tabs.Count; $i++) {
                $tabName = $tabs[$i].Current.Name
                Write-Host "Tab $($i+1): $tabName" -ForegroundColor White
            }
        }
        else {
            Write-Host "Could not find tabs using UI Automation." -ForegroundColor Yellow
            
            # Alternative approach: Try to count tabs from window title
            $title = $EdgeProcess.MainWindowTitle
            if ($title -match "and (\d+) more pages") {
                $tabCount = [int]$matches[1] + 1
                Write-Host "Based on window title, there are approximately $tabCount tabs open." -ForegroundColor Yellow
                
                # Try to get current tab URL
                $currentUrl = Get-CurrentUrl
                if ($currentUrl) {
                    Write-Host "Current tab URL: $currentUrl" -ForegroundColor Green
                }
            }
        }
    }
    catch {
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Process each Edge window
foreach ($process in $edgeProcesses) {
    Write-Host "`nAnalyzing Edge process (PID: $($process.Id))..." -ForegroundColor Cyan
    Get-EdgeTabs -EdgeProcess $process
}

Write-Host "`nAnalysis complete." -ForegroundColor Cyan
