# PowerShell script to open Windows Copilot and chat with it

# Add reference to Windows Forms for SendKeys
Add-Type -AssemblyName System.Windows.Forms

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class KeyboardSend
{
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    public const int KEYEVENTF_EXTENDEDKEY = 0x0001;
    public const int KEYEVENTF_KEYUP = 0x0002;
    
    public const byte VK_LWIN = 0x5B;  // Left Windows key
    public const byte VK_C = 0x43;     // C key
    public const byte VK_RETURN = 0x0D; // Enter key
    
    public static void SendWinC()
    {
        // Press Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Press C key
        keybd_event(VK_C, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release C key
        keybd_event(VK_C, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static void SendEnter()
    {
        // Press Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
}
"@

# Function to wait for a specified time
function Wait-Time {
    param (
        [int]$Milliseconds
    )
    
    Start-Sleep -Milliseconds $Milliseconds
}

# Messages to send to Copilot
$messages = @(
    "Hello Copilot! I'm chatting with you through an automated script.",
    "Can you tell me what you can help me with?",
    "What's the weather like today?",
    "Can you write a short poem about artificial intelligence?"
)

Write-Host "Opening Windows Copilot using Win+C shortcut..."
[KeyboardSend]::SendWinC()

# Wait for Copilot to open
Write-Host "Waiting for Copilot to open..."
Wait-Time 3000

# Send each message and wait for a response
foreach ($message in $messages) {
    Write-Host "Sending message: $message"
    
    # Type the message
    [System.Windows.Forms.SendKeys]::SendWait($message)
    Wait-Time 1000
    
    # Send Enter to submit the message
    [KeyboardSend]::SendEnter()
    
    # Wait for Copilot to respond
    Write-Host "Waiting for Copilot to respond..."
    Wait-Time 5000
}

Write-Host "Chat with Copilot completed!"
