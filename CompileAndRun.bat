@echo off
echo Windows Control Center - Compilation and Execution Script
echo ========================================================
echo.

REM Check if .NET SDK is installed
where dotnet >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: .NET SDK not found.
    echo Please install .NET 6.0 SDK or later from https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

REM Check .NET version
for /f "tokens=*" %%a in ('dotnet --version') do set DOTNET_VERSION=%%a
echo Found .NET SDK version: %DOTNET_VERSION%
echo.

REM Create app icon
echo Creating application icon...
cd WindowsControlCenter
powershell -ExecutionPolicy Bypass -File CreateAppIcon.ps1
echo.

REM Build the application
echo Building Windows Control Center...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Build failed.
    cd ..
    pause
    exit /b 1
)
echo.

REM Run the application
echo Running Windows Control Center...
echo.
echo NOTE: Some features require administrator privileges.
echo If you need full functionality, close this window and run as administrator.
echo.
dotnet run
cd ..

pause
