# Network Scanner Script
# This script scans your local network to identify connected devices and their activity

# Function to get the local IP address and subnet
function Get-LocalNetwork {
    $networkInfo = Get-NetIPAddress | Where-Object {
        $_.AddressFamily -eq "IPv4" -and 
        $_.PrefixOrigin -ne "WellKnown" -and 
        $_.IPAddress -ne "127.0.0.1"
    } | Select-Object -First 1
    
    if ($networkInfo) {
        $ipAddress = $networkInfo.IPAddress
        $prefixLength = $networkInfo.PrefixLength
        
        # Calculate subnet
        $ipBytes = $ipAddress.Split('.') | ForEach-Object { [byte]$_ }
        $mask = [math]::Pow(2, $prefixLength) - 1
        $mask = $mask -shl (32 - $prefixLength)
        
        $netBytes = @(0, 0, 0, 0)
        for ($i = 0; $i -lt 4; $i++) {
            $netBytes[$i] = $ipBytes[$i] -band (($mask -shr (8 * (3 - $i))) -band 0xFF)
        }
        
        $networkAddress = $netBytes -join '.'
        
        return @{
            IPAddress = $ipAddress
            NetworkAddress = $networkAddress
            PrefixLength = $prefixLength
        }
    } else {
        Write-Error "Could not determine local network information."
        return $null
    }
}

# Function to scan the network for active devices
function Scan-Network {
    param (
        [string]$NetworkAddress,
        [int]$PrefixLength
    )
    
    Write-Host "Scanning network for active devices..." -ForegroundColor Cyan
    
    # Calculate the number of hosts in the subnet
    $hostCount = [math]::Pow(2, (32 - $PrefixLength)) - 2
    
    # For a /24 subnet, scan all addresses from .1 to .254
    $baseIP = $NetworkAddress.Substring(0, $NetworkAddress.LastIndexOf('.') + 1)
    $devices = @()
    
    # Limit the scan to a reasonable number of addresses
    $maxScan = [Math]::Min($hostCount, 254)
    
    # Create a runspace pool for parallel scanning
    $runspacePool = [runspacefactory]::CreateRunspacePool(1, 20)
    $runspacePool.Open()
    
    $runspaces = @()
    $scriptBlock = {
        param($ip)
        $ping = New-Object System.Net.NetworkInformation.Ping
        try {
            $reply = $ping.Send($ip, 500)
            if ($reply.Status -eq 'Success') {
                try {
                    $hostEntry = [System.Net.Dns]::GetHostEntry($ip)
                    $hostname = $hostEntry.HostName
                } catch {
                    $hostname = "Unknown"
                }
                
                return @{
                    IPAddress = $ip
                    Hostname = $hostname
                    Status = "Online"
                    ResponseTime = $reply.RoundtripTime
                }
            }
        } catch {
            # Ignore errors
        }
        return $null
    }
    
    # Start ping scans in parallel
    for ($i = 1; $i -le $maxScan; $i++) {
        $ip = $baseIP + $i
        $runspace = [powershell]::Create().AddScript($scriptBlock).AddArgument($ip)
        $runspace.RunspacePool = $runspacePool
        $runspaces += [PSCustomObject]@{
            Runspace = $runspace
            Status = $runspace.BeginInvoke()
            IP = $ip
        }
    }
    
    # Collect results
    foreach ($runspace in $runspaces) {
        $result = $runspace.Runspace.EndInvoke($runspace.Status)
        if ($result) {
            $devices += $result
        }
        $runspace.Runspace.Dispose()
    }
    
    $runspacePool.Close()
    $runspacePool.Dispose()
    
    return $devices
}

# Function to get additional information about devices
function Get-DeviceDetails {
    param (
        [array]$Devices
    )
    
    Write-Host "Getting additional details for discovered devices..." -ForegroundColor Cyan
    
    foreach ($device in $Devices) {
        # Try to get MAC address
        try {
            $arp = arp -a $device.IPAddress | Select-String $device.IPAddress
            if ($arp) {
                $macAddress = ($arp -split '\s+')[3]
                $device | Add-Member -MemberType NoteProperty -Name "MACAddress" -Value $macAddress -Force
            } else {
                $device | Add-Member -MemberType NoteProperty -Name "MACAddress" -Value "Unknown" -Force
            }
        } catch {
            $device | Add-Member -MemberType NoteProperty -Name "MACAddress" -Value "Unknown" -Force
        }
        
        # Check for open ports
        $commonPorts = @(21, 22, 23, 25, 53, 80, 443, 445, 3389, 8080)
        $openPorts = @()
        
        foreach ($port in $commonPorts) {
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            try {
                $result = $tcpClient.BeginConnect($device.IPAddress, $port, $null, $null)
                $success = $result.AsyncWaitHandle.WaitOne(100)
                if ($success) {
                    $openPorts += $port
                }
            } catch {
                # Ignore errors
            } finally {
                $tcpClient.Close()
            }
        }
        
        $device | Add-Member -MemberType NoteProperty -Name "OpenPorts" -Value $openPorts -Force
    }
    
    return $Devices
}

# Function to check for unusual network activity
function Check-NetworkActivity {
    param (
        [array]$Devices
    )
    
    Write-Host "Checking for unusual network activity..." -ForegroundColor Cyan
    
    $suspiciousActivity = @()
    
    # Get current network connections
    $connections = Get-NetTCPConnection | Where-Object { $_.State -eq "Established" }
    
    foreach ($device in $Devices) {
        $deviceConnections = $connections | Where-Object { $_.RemoteAddress -eq $device.IPAddress }
        
        if ($deviceConnections) {
            $device | Add-Member -MemberType NoteProperty -Name "ActiveConnections" -Value $deviceConnections.Count -Force
            
            # Check for suspicious ports
            $suspiciousPorts = @(4444, 31337, 1337, 6667, 6697)
            $hasUnusualPorts = $deviceConnections | Where-Object { $suspiciousPorts -contains $_.RemotePort }
            
            if ($hasUnusualPorts) {
                $suspiciousActivity += @{
                    IPAddress = $device.IPAddress
                    Hostname = $device.Hostname
                    Reason = "Connection on suspicious port"
                    Details = "Connected on port(s): $($hasUnusualPorts.RemotePort -join ', ')"
                }
            }
            
            # Check for high number of connections
            if ($deviceConnections.Count -gt 10) {
                $suspiciousActivity += @{
                    IPAddress = $device.IPAddress
                    Hostname = $device.Hostname
                    Reason = "High number of connections"
                    Details = "$($deviceConnections.Count) active connections"
                }
            }
        } else {
            $device | Add-Member -MemberType NoteProperty -Name "ActiveConnections" -Value 0 -Force
        }
    }
    
    return @{
        Devices = $Devices
        SuspiciousActivity = $suspiciousActivity
    }
}

# Main execution
Clear-Host
Write-Host "===== Network Scanner =====" -ForegroundColor Green
Write-Host "Scanning your network for connected devices and activity..." -ForegroundColor Yellow

# Get local network information
$networkInfo = Get-LocalNetwork
if (-not $networkInfo) {
    Write-Host "Failed to determine network information. Exiting." -ForegroundColor Red
    exit
}

Write-Host "Your IP address: $($networkInfo.IPAddress)" -ForegroundColor Green
Write-Host "Network address: $($networkInfo.NetworkAddress)" -ForegroundColor Green
Write-Host "Subnet mask: /$($networkInfo.PrefixLength)" -ForegroundColor Green
Write-Host ""

# Scan network for devices
$devices = Scan-Network -NetworkAddress $networkInfo.NetworkAddress -PrefixLength $networkInfo.PrefixLength

if ($devices.Count -eq 0) {
    Write-Host "No devices found on the network." -ForegroundColor Yellow
    exit
}

Write-Host "Found $($devices.Count) devices on your network." -ForegroundColor Green
Write-Host ""

# Get additional details
$devicesWithDetails = Get-DeviceDetails -Devices $devices

# Check for unusual activity
$activityResults = Check-NetworkActivity -Devices $devicesWithDetails

# Display results
Write-Host "===== Connected Devices =====" -ForegroundColor Green
$activityResults.Devices | Format-Table -Property IPAddress, Hostname, MACAddress, Status, ResponseTime, ActiveConnections, @{Name="OpenPorts"; Expression={$_.OpenPorts -join ", "}} -AutoSize

if ($activityResults.SuspiciousActivity.Count -gt 0) {
    Write-Host "===== Suspicious Activity Detected =====" -ForegroundColor Red
    foreach ($activity in $activityResults.SuspiciousActivity) {
        Write-Host "Device: $($activity.IPAddress) ($($activity.Hostname))" -ForegroundColor Red
        Write-Host "Reason: $($activity.Reason)" -ForegroundColor Red
        Write-Host "Details: $($activity.Details)" -ForegroundColor Red
        Write-Host ""
    }
} else {
    Write-Host "No suspicious activity detected." -ForegroundColor Green
}

Write-Host "Scan completed." -ForegroundColor Green
