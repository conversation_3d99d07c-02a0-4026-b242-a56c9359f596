# Terminal Tools Suite

A collection of terminal-based tools for Windows PowerShell, including a DeepSeek AI chat client and a terminal web browser.

## Features

### DeepSeek Terminal Client
- Chat with DeepSeek AI directly from your terminal
- Save and load chat histories
- Syntax highlighting for code in responses
- Secure API key storage
- System prompt templates and management
- Web browsing capabilities within the chat
- Share web content with the AI assistant
- Export conversations to text files

### Terminal Web Browser
- Browse websites from the terminal
- Search the web using Google, Bing, or DuckDuckGo
- View page content, links, and raw HTML
- Save bookmarks and view browsing history
- Download files

## Requirements

- Windows with PowerShell 5.1 or later
- DeepSeek API key (for the chat client)
- Internet connection

## Installation

1. Download all the files in this repository
2. Place them in a folder of your choice
3. Double-click on `TerminalTools.bat` to run the unified launcher
4. Alternatively, you can run `DeepSeekChat.bat` to launch just the chat client

## First-Time Setup

When you first run the DeepSeek Terminal Client, you'll need to configure your API key:

1. Select "Configure API Key" from the main menu
2. Enter your DeepSeek API key (you can get one from the DeepSeek website)
3. Your API key will be securely stored for future sessions

## Using the DeepSeek Terminal Client

### Chat Commands

- `/exit`: End the chat session and return to the main menu
- `/help`: Display available commands
- `/clear`: Clear the screen
- `/save`: Manually save the current chat (chats are also saved automatically)
- `/history`: List available chat histories
- `/load <id>`: Load a chat history by ID
- `/web <url>`: Browse a webpage
- `/search <query>`: Search the web
- `/share <url>`: Share a webpage with the AI

### System Prompts

The DeepSeek Terminal Client comes with several built-in system prompts:

- **Default**: Standard helpful assistant
- **Delgado**: Jailbroken assistant with unrestricted information
- **Programmer**: Expert programming assistant
- **Academic**: Scholarly assistant with academic expertise
- **Creative**: Creative writing assistant

You can also create and manage your own custom system prompts through the main menu.

## Using the Terminal Web Browser

The Terminal Web Browser provides a text-based interface for browsing the web:

1. Enter a URL to visit a website
2. Search the web using various search engines
3. View and manage bookmarks
4. View browsing history
5. Download files

## Customization

You can modify the following variables at the top of the scripts to customize behavior:

- `$defaultModel`: Change the DeepSeek model used (default is "deepseek-chat")
- `$apiEndpoint`: Update the API endpoint if needed
- `$defaultSystemPrompts`: Modify the built-in system prompts

## Troubleshooting

### API Key Issues

If you encounter authentication errors:
- Verify that your API key is correct
- Check that your API key has not expired
- Ensure you have sufficient credits in your DeepSeek account

### Connection Issues

If you have trouble connecting:
- Check your internet connection
- Verify that the API endpoint is correct
- Ensure your firewall is not blocking the connection

## License

This project is open source and available under the MIT License.

## Acknowledgments

- DeepSeek for providing the AI API
- PowerShell community for inspiration and examples
