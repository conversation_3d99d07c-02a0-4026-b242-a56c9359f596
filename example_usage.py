#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example usage of the EUR/USD Trading Algorithm
"""

from eurusd_trading_algorithm import EURUSDTradingAlgorithm
import argparse

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='EUR/USD Trading Algorithm')
    parser.add_argument('--mode', choices=['backtest', 'monitor'], default='backtest',
                        help='Operation mode: backtest or monitor')
    parser.add_argument('--period', default='6mo',
                        help='Period to fetch data for (e.g., 1d, 1mo, 6mo, 1y)')
    parser.add_argument('--interval', default='15m',
                        help='Interval between data points (e.g., 1m, 15m, 1h, 1d)')
    parser.add_argument('--rsi-period', type=int, default=14,
                        help='Period for RSI calculation')
    parser.add_argument('--rsi-overbought', type=int, default=70,
                        help='RSI level considered overbought')
    parser.add_argument('--rsi-oversold', type=int, default=30,
                        help='RSI level considered oversold')
    parser.add_argument('--macd-fast', type=int, default=12,
                        help='Fast period for MACD calculation')
    parser.add_argument('--macd-slow', type=int, default=26,
                        help='Slow period for MACD calculation')
    parser.add_argument('--macd-signal', type=int, default=9,
                        help='Signal period for MACD calculation')
    parser.add_argument('--initial-capital', type=float, default=10000,
                        help='Initial capital for backtesting')
    parser.add_argument('--position-size', type=float, default=0.1,
                        help='Fraction of capital to use for each trade (0-1)')
    parser.add_argument('--monitor-interval', type=int, default=15,
                        help='Interval in minutes to refresh data when monitoring')
    
    args = parser.parse_args()
    
    # Create the trading algorithm with the specified parameters
    algo = EURUSDTradingAlgorithm(
        rsi_period=args.rsi_period,
        rsi_overbought=args.rsi_overbought,
        rsi_oversold=args.rsi_oversold,
        macd_fast=args.macd_fast,
        macd_slow=args.macd_slow,
        macd_signal=args.macd_signal
    )
    
    # Fetch data
    data = algo.fetch_data(period=args.period, interval=args.interval)
    
    if data is None:
        print("Failed to fetch data. Exiting.")
        return
    
    # Generate signals
    signals = algo.generate_signals()
    
    if args.mode == 'backtest':
        # Plot signals
        algo.plot_signals()
        
        # Backtest the strategy
        backtest_results = algo.backtest(
            initial_capital=args.initial_capital,
            position_size=args.position_size
        )
    else:  # monitor mode
        # Monitor current market
        algo.monitor_current_market(interval_minutes=args.monitor_interval)

if __name__ == "__main__":
    main()
