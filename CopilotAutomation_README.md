# Windows Copilot Automation

This tool helps automate interactions with Windows Copilot by sending predefined messages about CPU evolution and AI technology.

## Setup Instructions

1. **Install AutoHotkey**:
   - Download and install AutoHotkey from the official website: https://www.autohotkey.com/
   - Choose the current version (v1.1)

2. **Run the script**:
   - Right-click on either `CopilotAutomation.ahk` or `CopilotChatBot.ahk`
   - Select "Run Script" from the context menu

3. **Make sure Windows Copilot is open**:
   - Press Win+C or click the Copilot icon in your taskbar
   - The script will try to find and activate the Copilot window

## Using the Basic Script (CopilotAutomation.ahk)

This script will automatically try to send a message to Windows Copilot when launched.

- **Hotkey**: Press `Ctrl+Alt+C` to resend the message

## Using the Advanced Script (CopilotChatBot.ahk)

This script contains multiple predefined messages about CPU evolution and AI that can be sent sequentially.

### Hotkeys:

- `Ctrl+Alt+N`: Send the next message in the sequence
- `Ctrl+Alt+R`: Reset to the first message
- `Ctrl+Alt+C`: Enter and send a custom message
- `Ctrl+Alt+H`: Show help information

### Predefined Messages:

1. "Tell me about the evolution of CPUs and how they have affected AI technology development."
2. "How have advancements in processors like GPUs and TPUs specifically enabled modern AI models?"
3. "What are the key CPU innovations that made deep learning practical?"
4. "How has Moore's Law influenced the development of AI systems?"
5. "What role did parallel processing play in advancing AI capabilities?"
6. "How have specialized AI chips changed the landscape of machine learning?"
7. "What are the current CPU/GPU limitations holding back AI progress?"
8. "How are quantum processors expected to impact future AI development?"

## Troubleshooting

- If the script can't find the Copilot window, make sure Copilot is open and visible
- If messages aren't being sent correctly, try increasing the sleep times in the script
- If you encounter issues, right-click the AutoHotkey icon in the system tray and select "Exit" to stop the script

## Customization

You can edit the script to add your own messages or modify the existing ones:
1. Right-click on the .ahk file
2. Select "Edit Script"
3. Modify the messages array
4. Save the file and run it again
