#!/usr/bin/env python3
"""
PrivateFox Browser Setup Script
This script installs the required dependencies for the PrivateFox browser.
"""

import os
import sys
import subprocess
import platform

def print_header():
    """Print the setup header"""
    print("=" * 60)
    print("PrivateFox Browser Setup")
    print("A Private, Fast, and Reliable Web Browser")
    print("=" * 60)
    print()

def check_python_version():
    """Check if the Python version is compatible"""
    print("Checking Python version...")
    major, minor = sys.version_info[:2]
    if major < 3 or (major == 3 and minor < 8):
        print(f"Error: Python 3.8 or higher is required. You have Python {major}.{minor}.")
        return False
    print(f"Python {major}.{minor} detected. ✓")
    return True

def install_dependencies():
    """Install the required dependencies"""
    print("\nInstalling required dependencies...")
    
    # List of required packages
    packages = [
        "PyQt6",
        "PyQt6-WebEngine",
    ]
    
    try:
        # Install packages using pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        for package in packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print("All dependencies installed successfully. ✓")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False

def create_desktop_shortcut():
    """Create a desktop shortcut for the browser"""
    print("\nCreating desktop shortcut...")
    
    try:
        # Get the path to the script
        script_path = os.path.abspath("private_browser.py")
        
        if platform.system() == "Windows":
            # Windows shortcut
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop_path, "PrivateFox.lnk")
            
            # Create shortcut using PowerShell
            ps_script = f"""
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
            $Shortcut.TargetPath = "{sys.executable}"
            $Shortcut.Arguments = "{script_path}"
            $Shortcut.WorkingDirectory = "{os.path.dirname(script_path)}"
            $Shortcut.Description = "PrivateFox - A Private Web Browser"
            $Shortcut.Save()
            """
            
            with open("create_shortcut.ps1", "w") as f:
                f.write(ps_script)
                
            subprocess.call(["powershell", "-ExecutionPolicy", "Bypass", "-File", "create_shortcut.ps1"])
            os.remove("create_shortcut.ps1")
            
        elif platform.system() == "Linux":
            # Linux .desktop file
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            desktop_file_path = os.path.join(desktop_path, "privatefox.desktop")
            
            desktop_content = f"""[Desktop Entry]
Type=Application
Name=PrivateFox
Comment=A Private, Fast, and Reliable Web Browser
Exec={sys.executable} {script_path}
Terminal=false
Categories=Network;WebBrowser;
"""
            
            with open(desktop_file_path, "w") as f:
                f.write(desktop_content)
                
            # Make it executable
            os.chmod(desktop_file_path, 0o755)
            
        elif platform.system() == "Darwin":  # macOS
            # macOS .command file
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            command_file_path = os.path.join(desktop_path, "PrivateFox.command")
            
            command_content = f"""#!/bin/bash
cd "{os.path.dirname(script_path)}"
"{sys.executable}" "{script_path}"
"""
            
            with open(command_file_path, "w") as f:
                f.write(command_content)
                
            # Make it executable
            os.chmod(command_file_path, 0o755)
            
        print("Desktop shortcut created successfully. ✓")
        return True
    except Exception as e:
        print(f"Error creating desktop shortcut: {e}")
        return False

def create_launcher_script():
    """Create a launcher script for the browser"""
    print("\nCreating launcher script...")
    
    try:
        # Get the path to the script
        script_path = os.path.abspath("private_browser.py")
        
        if platform.system() == "Windows":
            # Windows batch file
            with open("launch_privatefox.bat", "w") as f:
                f.write(f'@echo off\n"{sys.executable}" "{script_path}"\n')
                
        else:
            # Unix shell script
            with open("launch_privatefox.sh", "w") as f:
                f.write(f'#!/bin/bash\n"{sys.executable}" "{script_path}"\n')
                
            # Make it executable
            os.chmod("launch_privatefox.sh", 0o755)
            
        print("Launcher script created successfully. ✓")
        return True
    except Exception as e:
        print(f"Error creating launcher script: {e}")
        return False

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Install dependencies
    if not install_dependencies():
        input("Failed to install dependencies. Press Enter to exit...")
        return
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    # Create launcher script
    create_launcher_script()
    
    print("\n" + "=" * 60)
    print("Setup completed successfully!")
    print("You can now run PrivateFox using the desktop shortcut")
    print("or by running the launcher script.")
    print("=" * 60)
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
