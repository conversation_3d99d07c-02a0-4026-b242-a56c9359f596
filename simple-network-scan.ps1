# Simple Network Scanner Script
# This script scans your local network to identify connected devices

# Get all network adapters with IPv4 addresses
$networkAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
Write-Host "Active Network Adapters:" -ForegroundColor Green
$networkAdapters | Format-Table -Property Name, InterfaceDescription, Status, MacAddress -AutoSize

# Get all IP configurations
Write-Host "`nIP Configurations:" -ForegroundColor Green
$ipConfigs = Get-NetIPConfiguration | Where-Object { $_.IPv4Address -ne $null }
$ipConfigs | Format-Table -Property InterfaceAlias, IPv4Address, IPv4DefaultGateway -AutoSize

# Function to scan a specific subnet
function Scan-Subnet {
    param (
        [string]$Subnet
    )
    
    Write-Host "`nScanning subnet $Subnet..." -ForegroundColor Cyan
    
    # Extract the base IP
    $baseIP = $Subnet.Substring(0, $Subnet.LastIndexOf('.') + 1)
    
    $results = @()
    
    # Scan IP range from 1 to 254
    1..254 | ForEach-Object {
        $ip = $baseIP + $_
        $ping = New-Object System.Net.NetworkInformation.Ping
        
        try {
            $reply = $ping.Send($ip, 500)
            if ($reply.Status -eq 'Success') {
                try {
                    $hostEntry = [System.Net.Dns]::GetHostEntry($ip)
                    $hostname = $hostEntry.HostName
                } catch {
                    $hostname = "Unknown"
                }
                
                # Try to get MAC address using ARP
                $arpResult = arp -a $ip | Where-Object { $_ -match $ip }
                if ($arpResult) {
                    $macAddress = ($arpResult -split '\s+')[3]
                } else {
                    $macAddress = "Unknown"
                }
                
                $results += [PSCustomObject]@{
                    IPAddress = $ip
                    Hostname = $hostname
                    MACAddress = $macAddress
                    ResponseTime = $reply.RoundtripTime
                }
                
                Write-Host "Found device: $ip ($hostname)" -ForegroundColor Green
            }
        } catch {
            # Ignore errors
        }
    }
    
    return $results
}

# Function to check active connections
function Get-ActiveConnections {
    Write-Host "`nChecking active network connections..." -ForegroundColor Cyan
    
    $connections = Get-NetTCPConnection | Where-Object { $_.State -eq "Established" }
    
    # Group connections by remote address
    $groupedConnections = $connections | Group-Object -Property RemoteAddress
    
    $results = @()
    
    foreach ($group in $groupedConnections) {
        try {
            $hostEntry = [System.Net.Dns]::GetHostEntry($group.Name)
            $hostname = $hostEntry.HostName
        } catch {
            $hostname = "Unknown"
        }
        
        $results += [PSCustomObject]@{
            RemoteAddress = $group.Name
            Hostname = $hostname
            ConnectionCount = $group.Count
            Ports = ($group.Group | Select-Object -ExpandProperty RemotePort | Sort-Object -Unique) -join ", "
            LocalPorts = ($group.Group | Select-Object -ExpandProperty LocalPort | Sort-Object -Unique) -join ", "
        }
    }
    
    return $results
}

# Scan each subnet
$allDevices = @()

foreach ($ipConfig in $ipConfigs) {
    $ipAddress = $ipConfig.IPv4Address.IPAddress
    $subnet = $ipAddress.Substring(0, $ipAddress.LastIndexOf('.') + 1) + "0"
    
    $devices = Scan-Subnet -Subnet $subnet
    $allDevices += $devices
}

# Display results
Write-Host "`n===== Connected Devices =====" -ForegroundColor Green
$allDevices | Format-Table -Property IPAddress, Hostname, MACAddress, ResponseTime -AutoSize

# Check active connections
$activeConnections = Get-ActiveConnections
Write-Host "`n===== Active Network Connections =====" -ForegroundColor Green
$activeConnections | Format-Table -Property RemoteAddress, Hostname, ConnectionCount, Ports, LocalPorts -AutoSize

# Check for potential unusual activity
$suspiciousConnections = $activeConnections | Where-Object { 
    $_.ConnectionCount -gt 10 -or 
    $_.Ports -match "(4444|31337|1337|6667|6697)" 
}

if ($suspiciousConnections) {
    Write-Host "`n===== Potential Unusual Activity =====" -ForegroundColor Yellow
    $suspiciousConnections | Format-Table -Property RemoteAddress, Hostname, ConnectionCount, Ports -AutoSize
} else {
    Write-Host "`nNo unusual network activity detected." -ForegroundColor Green
}

# Show listening ports
Write-Host "`n===== Listening Ports =====" -ForegroundColor Green
Get-NetTCPConnection -State Listen | 
    Select-Object LocalAddress, LocalPort, @{Name="Process";Expression={(Get-Process -Id $_.OwningProcess).ProcessName}} | 
    Sort-Object LocalPort |
    Format-Table -AutoSize

Write-Host "`nScan completed." -ForegroundColor Green
