# PowerShell script to chat with already-open Copilot

# Add reference to Windows Forms for SendKeys
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class KeyboardSend
{
    [DllImport("user32.dll")]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
    
    [DllImport("user32.dll")]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
    
    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);
    
    public const int KEYEVENTF_EXTENDEDKEY = 0x0001;
    public const int KEYEVENTF_KEYUP = 0x0002;
    
    public const byte VK_RETURN = 0x0D; // Enter key
    
    public static void SendEnter()
    {
        // Press Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
        
        // Release Enter key
        keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP | KEYEVENTF_EXTENDEDKEY, UIntPtr.Zero);
    }
    
    public static bool ActivateCopilotWindow()
    {
        // Try to find and activate Copilot window
        IntPtr hWnd = FindWindow(null, "Copilot");
        if (hWnd != IntPtr.Zero)
        {
            return SetForegroundWindow(hWnd);
        }
        return false;
    }
}
"@

# Function to wait for a specified time
function Wait-Time {
    param (
        [int]$Milliseconds
    )
    
    Start-Sleep -Milliseconds $Milliseconds
}

# Function to take a screenshot
function Take-Screenshot {
    param (
        [string]$FilePath
    )
    
    $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
    $bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.CopyFromScreen($bounds.X, $bounds.Y, 0, 0, $bounds.Size)
    $bitmap.Save($FilePath)
    $graphics.Dispose()
    $bitmap.Dispose()
    
    Write-Host "Screenshot saved to $FilePath"
}

# Messages to send to Copilot
$messages = @(
    "Hello Copilot! I'm sending this message through an automated script.",
    "Can you tell me about the latest advancements in AI technology?",
    "What are some interesting facts about space exploration?",
    "Write a short poem about technology and nature."
)

# Try to activate the Copilot window
Write-Host "Trying to activate Copilot window..."
$activated = [KeyboardSend]::ActivateCopilotWindow()

if ($activated) {
    Write-Host "Successfully activated Copilot window!"
} else {
    Write-Host "Could not find Copilot window. Assuming it's already in focus."
}

# Take a screenshot before starting
Take-Screenshot -FilePath "terminator-test\copilot_before_chat.png"

# Wait a moment to ensure Copilot is ready
Wait-Time 1000

# Send each message and wait for a response
foreach ($message in $messages) {
    Write-Host "Sending message: $message"
    
    # Type the message
    [System.Windows.Forms.SendKeys]::SendWait($message)
    Wait-Time 1000
    
    # Take a screenshot before sending
    Take-Screenshot -FilePath "terminator-test\copilot_before_send_$($messages.IndexOf($message)).png"
    
    # Send Enter to submit the message
    [KeyboardSend]::SendEnter()
    
    # Wait for Copilot to respond
    Write-Host "Waiting for Copilot to respond..."
    $waitTime = 8000  # Longer wait time for more complex responses
    Wait-Time $waitTime
    
    # Take a screenshot after response
    Take-Screenshot -FilePath "terminator-test\copilot_after_response_$($messages.IndexOf($message)).png"
}

# Take a final screenshot
Take-Screenshot -FilePath "terminator-test\copilot_final_chat.png"

Write-Host "Chat with Copilot completed!"
