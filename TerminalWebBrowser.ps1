# TerminalWebBrowser.ps1
# A collection of PowerShell functions for terminal-based web browsing in Windows
# Created by Augment Agent

#Requires -Version 5.1

# Add required .NET types
Add-Type -AssemblyName System.Web
Add-Type -AssemblyName System.Net.Http

# Global variables
$global:userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
$global:defaultDownloadPath = Join-Path $env:USERPROFILE "Downloads"
$global:historyFile = Join-Path $env:USERPROFILE ".terminal-browser-history.json"
$global:bookmarksFile = Join-Path $env:USERPROFILE ".terminal-browser-bookmarks.json"
$global:lastResponse = $null
$global:lastUrl = ""
$global:lastVisitedUrl = ""
$global:lastVisitedTitle = ""

# Initialize history and bookmarks
function Initialize-BrowserData {
    # Initialize history
    if (-not (Test-Path $global:historyFile)) {
        @{
            "visits" = @()
        } | ConvertTo-Json | Set-Content -Path $global:historyFile
    }

    # Initialize bookmarks
    if (-not (Test-Path $global:bookmarksFile)) {
        @{
            "bookmarks" = @()
        } | ConvertTo-Json | Set-Content -Path $global:bookmarksFile
    }
}

# Function to make HTTP requests
function Invoke-WebRequestWithProgress {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Uri,

        [string]$Method = "GET",

        [hashtable]$Headers = @{},

        [string]$Body = "",

        [switch]$IncludeRawContent
    )

    # Add User-Agent if not present
    if (-not $Headers.ContainsKey("User-Agent")) {
        $Headers["User-Agent"] = $global:userAgent
    }

    Write-Host "Fetching $Uri..." -ForegroundColor Cyan

    try {
        # Create HttpClient
        $handler = New-Object System.Net.Http.HttpClientHandler
        $handler.AutomaticDecompression = [System.Net.DecompressionMethods]::GZip -bor [System.Net.DecompressionMethods]::Deflate
        $client = New-Object System.Net.Http.HttpClient($handler)

        # Set headers
        foreach ($key in $Headers.Keys) {
            $client.DefaultRequestHeaders.Add($key, $Headers[$key])
        }

        # Create request
        $request = $null
        if ($Method -eq "GET") {
            $task = $client.GetAsync($Uri)
            $task.Wait()
            $response = $task.Result
        }
        elseif ($Method -eq "POST") {
            $content = New-Object System.Net.Http.StringContent($Body)
            $task = $client.PostAsync($Uri, $content)
            $task.Wait()
            $response = $task.Result
        }
        else {
            throw "Method $Method not supported"
        }

        # Check if successful
        $response.EnsureSuccessStatusCode() | Out-Null

        # Get content
        $contentTask = $response.Content.ReadAsStringAsync()
        $contentTask.Wait()
        $content = $contentTask.Result

        # Store last response
        $global:lastResponse = $response
        $global:lastUrl = $Uri

        # Add to history
        Add-ToHistory -Url $Uri -Title ($Uri -replace "https?://([^/]+).*", '$1')

        # Return content
        if ($IncludeRawContent) {
            return @{
                "Content" = $content
                "StatusCode" = $response.StatusCode
                "Headers" = $response.Headers
                "ContentType" = $response.Content.Headers.ContentType
            }
        }
        else {
            return $content
        }
    }
    catch {
        Write-Host "Error: $_" -ForegroundColor Red
        return $null
    }
    finally {
        if ($client) {
            $client.Dispose()
        }
    }
}

# Function to extract text content from HTML
function Get-TextFromHtml {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Html
    )

    # Simple regex-based approach to strip HTML tags
    $text = $Html -replace "<style>.*?</style>", "" -replace "<script>.*?</script>", ""
    $text = $text -replace "<.*?>", " " -replace "&nbsp;", " " -replace "&lt;", "<" -replace "&gt;", ">" -replace "&amp;", "&"
    $text = $text -replace "\s+", " "
    return $text.Trim()
}

# Function to extract links from HTML
function Get-LinksFromHtml {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Html,

        [string]$BaseUrl = ""
    )

    $links = @()
    try {
        $matches = [regex]::Matches($Html, '<a\s+(?:[^>]*?\s+)?href="([^"]*)"[^>]*>(.*?)</a>')

        foreach ($match in $matches) {
            $href = $match.Groups[1].Value
            $text = $match.Groups[2].Value -replace "<.*?>", ""

            # Handle relative URLs
            if ($href -notmatch "^https?://" -and $BaseUrl) {
                if ($href.StartsWith("/")) {
                    $baseUri = [System.Uri]$BaseUrl
                    $href = "$($baseUri.Scheme)://$($baseUri.Host)$href"
                }
                elseif (-not $href.StartsWith("#")) {
                    $href = "$BaseUrl/$href"
                }
            }

            $links += [PSCustomObject]@{
                Href = $href
                Text = $text.Trim()
            }
        }
    }
    catch {
        Write-Host "Error extracting links: $_" -ForegroundColor Red
    }

    return $links
}

# Function to add a visit to history
function Add-ToHistory {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Url,

        [string]$Title = ""
    )

    $history = Get-Content -Path $global:historyFile | ConvertFrom-Json

    $history.visits += [PSCustomObject]@{
        Url = $Url
        Title = $Title
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }

    # Keep only the last 100 entries
    if ($history.visits.Count -gt 100) {
        $history.visits = $history.visits | Select-Object -Last 100
    }

    $history | ConvertTo-Json | Set-Content -Path $global:historyFile
}

# Function to add a bookmark
function Add-Bookmark {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Url,

        [Parameter(Mandatory=$true)]
        [string]$Title,

        [string]$Folder = "Default"
    )

    $bookmarks = Get-Content -Path $global:bookmarksFile | ConvertFrom-Json

    $bookmarks.bookmarks += [PSCustomObject]@{
        Url = $Url
        Title = $Title
        Folder = $Folder
        DateAdded = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }

    $bookmarks | ConvertTo-Json | Set-Content -Path $global:bookmarksFile
    Write-Host "Bookmark added: $Title" -ForegroundColor Green
}

# Function to list bookmarks
function Get-Bookmarks {
    param (
        [string]$Folder = ""
    )

    $bookmarks = Get-Content -Path $global:bookmarksFile | ConvertFrom-Json

    if ($Folder) {
        $filteredBookmarks = $bookmarks.bookmarks | Where-Object { $_.Folder -eq $Folder }
    }
    else {
        $filteredBookmarks = $bookmarks.bookmarks
    }

    return $filteredBookmarks
}

# Function to search the web
function Search-Web {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Query,

        [ValidateSet("google", "bing", "duckduckgo")]
        [string]$Engine = "google"
    )

    $encodedQuery = [System.Web.HttpUtility]::UrlEncode($Query)

    switch ($Engine) {
        "google" {
            $searchUrl = "https://www.google.com/search?q=$encodedQuery"
        }
        "bing" {
            $searchUrl = "https://www.bing.com/search?q=$encodedQuery"
        }
        "duckduckgo" {
            $searchUrl = "https://duckduckgo.com/?q=$encodedQuery"
        }
    }

    $response = Invoke-WebRequestWithProgress -Uri $searchUrl

    # Extract search results
    $text = Get-TextFromHtml -Html $response
    $links = Get-LinksFromHtml -Html $response -BaseUrl $searchUrl

    # Display results
    Write-Host "Search results for: $Query" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor DarkGray

    $resultLinks = $links | Where-Object { $_.Href -notmatch "google|bing|duckduckgo" -and $_.Href -notmatch "javascript:" }

    for ($i = 0; $i -lt [Math]::Min(10, $resultLinks.Count); $i++) {
        Write-Host "[$i] " -ForegroundColor Yellow -NoNewline
        Write-Host $resultLinks[$i].Text -ForegroundColor White
        Write-Host "    " -NoNewline
        Write-Host $resultLinks[$i].Href -ForegroundColor DarkGray
        Write-Host ""
    }

    return $resultLinks
}

# Function to download a file
function Save-WebFile {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Url,

        [string]$OutputPath = "",

        [switch]$OpenAfterDownload
    )

    if (-not $OutputPath) {
        $fileName = $Url -replace ".*\/([^\/]+)$", '$1'
        if (-not $fileName -or $fileName -eq $Url) {
            $fileName = "download_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        }
        $OutputPath = Join-Path $global:defaultDownloadPath $fileName
    }

    Write-Host "Downloading $Url to $OutputPath..." -ForegroundColor Cyan

    try {
        # Create WebClient for download
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", $global:userAgent)

        # Add event handler for progress reporting
        $eventId = [guid]::NewGuid().ToString()
        $progressEventHandler = {
            param($sender, $e)
            $percentComplete = [int](($e.BytesReceived / $e.TotalBytesToReceive) * 100)
            Write-Progress -Activity "Downloading File" -Status "$percentComplete% Complete" -PercentComplete $percentComplete
        }

        [void]$webClient.add_DownloadProgressChanged($progressEventHandler)

        # Add event handler for completion
        $completedEventHandler = {
            param($sender, $e)
            Write-Progress -Activity "Downloading File" -Completed
            Write-Host "Download completed!" -ForegroundColor Green
        }

        [void]$webClient.add_DownloadFileCompleted($completedEventHandler)

        # Start download
        $webClient.DownloadFileAsync([System.Uri]$Url, $OutputPath)

        # Wait for download to complete
        while ($webClient.IsBusy) {
            Start-Sleep -Milliseconds 100
        }

        # Open file if requested
        if ($OpenAfterDownload) {
            Start-Process $OutputPath
        }

        return $OutputPath
    }
    catch {
        Write-Host "Error downloading file: $_" -ForegroundColor Red
        return $null
    }
}

# Function to view a webpage
function Show-WebPage {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Url,

        [switch]$RawHtml,

        [switch]$TextOnly,

        [switch]$Links
    )

    $response = Invoke-WebRequestWithProgress -Uri $Url -IncludeRawContent

    if (-not $response) {
        return
    }

    if ($RawHtml) {
        # Show raw HTML
        $response.Content
    }
    elseif ($TextOnly) {
        # Show text only
        $text = Get-TextFromHtml -Html $response.Content
        Write-Host $text
    }
    elseif ($Links.IsPresent) {
        # Show links
        $links = Get-LinksFromHtml -Html $response.Content -BaseUrl $Url

        for ($i = 0; $i -lt $links.Count; $i++) {
            Write-Host "[$i] " -ForegroundColor Yellow -NoNewline
            Write-Host $links[$i].Text -ForegroundColor White
            Write-Host "    " -NoNewline
            Write-Host $links[$i].Href -ForegroundColor DarkGray
        }

        return $links
    }
    else {
        # Show formatted content
        $title = [regex]::Match($response.Content, '<title>(.*?)</title>').Groups[1].Value

        Write-Host "Title: $title" -ForegroundColor Cyan
        Write-Host "URL: $Url" -ForegroundColor DarkGray
        Write-Host "Content-Type: $($response.ContentType)" -ForegroundColor DarkGray
        Write-Host "----------------------------------------" -ForegroundColor DarkGray

        $text = Get-TextFromHtml -Html $response.Content
        Write-Host $text.Substring(0, [Math]::Min(2000, $text.Length))

        if ($text.Length -gt 2000) {
            Write-Host "... (content truncated, use -TextOnly to see full content)" -ForegroundColor DarkGray
        }

        # Display links section
        Write-Host "`nLinks:" -ForegroundColor Cyan
        try {
            $links = Get-LinksFromHtml -Html $response.Content -BaseUrl $Url

            if ($links -and $links.Count -gt 0) {
                for ($i = 0; $i -lt [Math]::Min(5, $links.Count); $i++) {
                    Write-Host "[$i] " -ForegroundColor Yellow -NoNewline
                    Write-Host $links[$i].Text -ForegroundColor White
                    Write-Host "    " -NoNewline
                    Write-Host $links[$i].Href -ForegroundColor DarkGray
                }

                if ($links.Count -gt 5) {
                    Write-Host "... (and $($links.Count - 5) more links, use -Links to see all)" -ForegroundColor DarkGray
                }
            } else {
                Write-Host "No links found on this page." -ForegroundColor DarkGray
            }
        } catch {
            Write-Host "Error extracting links: $_" -ForegroundColor DarkGray
            Write-Host "Use the -Links parameter to try again with detailed error reporting." -ForegroundColor DarkGray
        }

        return $response
    }
}

# Main menu function
function Show-BrowserMenu {
    Clear-Host
    Write-Host "Terminal Web Browser" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor DarkGray

    Write-Host "`nOptions:" -ForegroundColor Cyan
    Write-Host "1. Visit a website" -ForegroundColor White
    Write-Host "2. Search the web" -ForegroundColor White
    Write-Host "3. View bookmarks" -ForegroundColor White
    Write-Host "4. View history" -ForegroundColor White
    Write-Host "5. Download a file" -ForegroundColor White
    Write-Host "6. Add current page to bookmarks" -ForegroundColor White
    Write-Host "7. Exit" -ForegroundColor White

    $choice = Read-Host "`nEnter your choice (1-7)"

    switch ($choice) {
        "1" {
            $url = Read-Host "Enter URL (include http:// or https://)"
            if ($url -notmatch "^https?://") {
                $url = "https://$url"
            }
            $response = Show-WebPage -Url $url
            $global:lastVisitedUrl = $url
            $global:lastVisitedTitle = [regex]::Match($response.Content, '<title>(.*?)</title>').Groups[1].Value
            if (-not $global:lastVisitedTitle) {
                $global:lastVisitedTitle = $url
            }
            Read-Host "`nPress Enter to continue"
            Show-BrowserMenu
        }
        "2" {
            $query = Read-Host "Enter search query"
            $engine = Read-Host "Enter search engine (google, bing, duckduckgo) [default: google]"
            if (-not $engine) {
                $engine = "google"
            }
            $results = Search-Web -Query $query -Engine $engine

            $choice = Read-Host "`nEnter result number to visit (or press Enter to return to menu)"
            if ($choice -match "^\d+$" -and [int]$choice -lt $results.Count) {
                $url = $results[[int]$choice].Href
                $response = Show-WebPage -Url $url
                $global:lastVisitedUrl = $url
                $global:lastVisitedTitle = $results[[int]$choice].Text
                Read-Host "`nPress Enter to continue"
            }

            Show-BrowserMenu
        }
        "3" {
            $bookmarks = Get-Bookmarks

            if ($bookmarks.Count -eq 0) {
                Write-Host "No bookmarks found." -ForegroundColor Yellow
                Read-Host "`nPress Enter to continue"
                Show-BrowserMenu
                return
            }

            Write-Host "Bookmarks:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $bookmarks.Count; $i++) {
                Write-Host "[$i] " -ForegroundColor Yellow -NoNewline
                Write-Host $bookmarks[$i].Title -ForegroundColor White
                Write-Host "    " -NoNewline
                Write-Host $bookmarks[$i].Url -ForegroundColor DarkGray
                Write-Host "    Folder: $($bookmarks[$i].Folder)" -ForegroundColor DarkGray
            }

            $choice = Read-Host "`nEnter bookmark number to visit (or press Enter to return to menu)"
            if ($choice -match "^\d+$" -and [int]$choice -lt $bookmarks.Count) {
                $url = $bookmarks[[int]$choice].Url
                $response = Show-WebPage -Url $url
                $global:lastVisitedUrl = $url
                $global:lastVisitedTitle = $bookmarks[[int]$choice].Title
                Read-Host "`nPress Enter to continue"
            }

            Show-BrowserMenu
        }
        "4" {
            $history = Get-Content -Path $global:historyFile | ConvertFrom-Json

            if ($history.visits.Count -eq 0) {
                Write-Host "No history found." -ForegroundColor Yellow
                Read-Host "`nPress Enter to continue"
                Show-BrowserMenu
                return
            }

            Write-Host "History:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $history.visits.Count; $i++) {
                Write-Host "[$i] " -ForegroundColor Yellow -NoNewline
                Write-Host $history.visits[$i].Title -ForegroundColor White
                Write-Host "    " -NoNewline
                Write-Host $history.visits[$i].Url -ForegroundColor DarkGray
                Write-Host "    $($history.visits[$i].Timestamp)" -ForegroundColor DarkGray
            }

            $choice = Read-Host "`nEnter history number to visit (or press Enter to return to menu)"
            if ($choice -match "^\d+$" -and [int]$choice -lt $history.visits.Count) {
                $url = $history.visits[[int]$choice].Url
                $response = Show-WebPage -Url $url
                $global:lastVisitedUrl = $url
                $global:lastVisitedTitle = $history.visits[[int]$choice].Title
                Read-Host "`nPress Enter to continue"
            }

            Show-BrowserMenu
        }
        "5" {
            $url = Read-Host "Enter URL of file to download"
            $outputPath = Read-Host "Enter output path (leave blank for default Downloads folder)"

            Save-WebFile -Url $url -OutputPath $outputPath
            Read-Host "`nPress Enter to continue"
            Show-BrowserMenu
        }
        "6" {
            if (-not $global:lastVisitedUrl) {
                Write-Host "No page has been visited yet." -ForegroundColor Yellow
                Read-Host "`nPress Enter to continue"
                Show-BrowserMenu
                return
            }

            $title = $global:lastVisitedTitle
            if (-not $title) {
                $title = Read-Host "Enter a title for this bookmark"
            } else {
                $confirmTitle = Read-Host "Bookmark title [$title] (press Enter to accept or type a new title)"
                if ($confirmTitle) {
                    $title = $confirmTitle
                }
            }

            $folder = Read-Host "Enter a folder name for this bookmark [Default]"
            if (-not $folder) {
                $folder = "Default"
            }

            Add-Bookmark -Url $global:lastVisitedUrl -Title $title -Folder $folder
            Read-Host "`nPress Enter to continue"
            Show-BrowserMenu
        }
        "7" {
            Write-Host "Exiting Terminal Web Browser. Goodbye!" -ForegroundColor Cyan
            return
        }
        default {
            Write-Host "Invalid choice. Please try again." -ForegroundColor Red
            Read-Host "`nPress Enter to continue"
            Show-BrowserMenu
        }
    }
}

# Initialize browser data
Initialize-BrowserData

# Start the browser
Show-BrowserMenu
