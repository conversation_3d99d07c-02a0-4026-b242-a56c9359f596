<!DOCTYPE html>
<html>
<head>
    <title>Edge Tab Lister</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #0078D7;
        }
        .tab-list {
            margin-top: 20px;
        }
        .tab-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .tab-title {
            font-weight: bold;
            color: #0078D7;
        }
        .tab-url {
            color: #666;
            word-break: break-all;
        }
        .instructions {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #0078D7;
            margin-bottom: 20px;
        }
        button {
            background-color: #0078D7;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a9e;
        }
    </style>
</head>
<body>
    <h1>Edge Tab Lister</h1>
    
    <div class="instructions">
        <p><strong>Instructions:</strong></p>
        <p>1. This page needs to be opened as a browser extension to access tab information.</p>
        <p>2. To use this, you need to:</p>
        <ul>
            <li>Go to Edge Extensions (edge://extensions/)</li>
            <li>Enable "Developer mode" (toggle in the bottom-left)</li>
            <li>Click "Load unpacked" and select the folder containing this HTML file and a manifest.json file</li>
        </ul>
        <p>3. Alternatively, you can use the Edge Task Manager to see basic tab information:</p>
        <ul>
            <li>Press Shift+Esc while in Edge</li>
            <li>Or click the "..." menu, then "More tools" > "Browser task manager"</li>
        </ul>
    </div>
    
    <button id="listTabs">List All Tabs (Extension Only)</button>
    
    <div id="tabList" class="tab-list">
        <p>Tab information will appear here if this is loaded as an extension.</p>
    </div>
    
    <script>
        document.getElementById('listTabs').addEventListener('click', function() {
            const tabListElement = document.getElementById('tabList');
            
            // Check if browser tabs API is available (only in extensions)
            if (typeof chrome !== 'undefined' && chrome.tabs) {
                chrome.tabs.query({}, function(tabs) {
                    tabListElement.innerHTML = '';
                    
                    if (tabs.length === 0) {
                        tabListElement.innerHTML = '<p>No tabs found.</p>';
                        return;
                    }
                    
                    tabs.forEach(function(tab, index) {
                        const tabElement = document.createElement('div');
                        tabElement.className = 'tab-item';
                        
                        const titleElement = document.createElement('div');
                        titleElement.className = 'tab-title';
                        titleElement.textContent = `Tab ${index + 1}: ${tab.title}`;
                        
                        const urlElement = document.createElement('div');
                        urlElement.className = 'tab-url';
                        urlElement.textContent = tab.url;
                        
                        tabElement.appendChild(titleElement);
                        tabElement.appendChild(urlElement);
                        
                        tabListElement.appendChild(tabElement);
                    });
                });
            } else {
                tabListElement.innerHTML = '<p>This feature is only available when loaded as a browser extension.</p>';
                tabListElement.innerHTML += '<p>Try using Edge Task Manager instead (Shift+Esc).</p>';
            }
        });
    </script>
</body>
</html>
