{"name": "terminator-mcp", "version": "0.1.0", "description": "A simple MCP server for Terminator", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts"}, "keywords": ["mcp", "terminator"], "author": "", "license": "ISC", "dependencies": {"@clack/prompts": "^0.7.0", "@modelcontextprotocol/sdk": "^1.8.0", "dotenv": "^16.4.5", "zod": "^3.23.8", "yargs": "^17.7.2", "desktop-use": "latest"}, "devDependencies": {"@types/node": "^20.14.2", "@types/yargs": "^17.0.32", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}