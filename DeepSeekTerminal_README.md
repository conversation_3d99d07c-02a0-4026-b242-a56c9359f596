# DeepSeek Terminal Client

A PowerShell-based terminal client for chatting with DeepSeek AI without using a web browser.

## Features

- Chat with DeepSeek AI directly from your terminal
- Save and load chat histories
- Syntax highlighting for code in responses
- Secure API key storage
- Jailbroken Delgado model support

## Requirements

- Windows with PowerShell 5.1 or later
- DeepSeek API key

## Installation

1. Download the `DeepSeekTerminal.ps1` and `DeepSeekChat.bat` files
2. Place them in a folder of your choice
3. Double-click on `DeepSeekChat.bat` to run the application

## First-Time Setup

1. When you first run the application, you'll need to configure your DeepSeek API key
2. Select option 3 from the main menu
3. Enter your API key when prompted
4. The key will be stored securely for future use

## Usage

### Main Menu

The main menu provides the following options:

1. **Start New Chat**: Begin a new conversation with DeepSeek AI
2. **Load Existing Chat**: Continue a previous conversation
3. **Configure API Key**: Set or update your DeepSeek API key
4. **Exit**: Close the application

### Chat Commands

While in a chat session, you can use the following commands:

- `/exit`: End the chat session and return to the main menu
- `/help`: Display available commands
- `/clear`: Clear the screen
- `/save`: Manually save the current chat (chats are also saved automatically)
- `/history`: List available chat histories
- `/load <id>`: Load a chat history by ID

## Customization

You can modify the following variables at the top of the script to customize the behavior:

- `$defaultModel`: Change the DeepSeek model used (default is "deepseek-chat")
- `$apiEndpoint`: Update the API endpoint if needed
- `$SystemPrompt`: Modify the system prompt to change the AI's behavior

## Troubleshooting

### API Key Issues

If you encounter authentication errors:
1. Select option 3 from the main menu to reconfigure your API key
2. Make sure you're using a valid DeepSeek API key

### Rate Limiting

If you see rate limit errors:
1. Wait a few minutes before sending more messages
2. Check your DeepSeek account for usage limits

### Script Execution Policy

If you get execution policy errors:
1. Run PowerShell as Administrator
2. Execute: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
3. Try running the script again

## Privacy and Security

- Your API key is stored securely using Windows Data Protection API
- Chat histories are stored locally on your computer
- No data is sent to any servers other than the official DeepSeek API

## License

This script is provided as-is with no warranty. Use at your own risk.
