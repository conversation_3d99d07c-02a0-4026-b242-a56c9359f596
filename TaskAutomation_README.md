# Task Automation Script

This AutoHotkey script allows you to automate repetitive tasks by defining a sequence of actions to be performed.

## Getting Started

1. Make sure AutoHotkey is installed on your system
2. Run the `TaskAutomation.ahk` script by double-clicking it
3. The script will show a message box with basic instructions
4. Use the hotkeys to control the automation

## Hotkeys

- **F8**: Run the automation sequence once
- **F9**: Run the automation sequence multiple times (based on the `repetitions` setting)
- **F10**: Reload the script (useful after making changes)
- **F12**: Exit the script
- **Ctrl+Alt+P**: Get the current mouse position (copied to clipboard)

## Customizing the Automation

To customize the script for your specific task, you need to modify the following sections:

### Configuration Section

```autohotkey
; Delay between actions in milliseconds
global actionDelay := 500

; Number of repetitions for the task
global repetitions := 5

; Target window title (partial match is fine)
global targetWindow := "Windows"
```

### Actions Array

The `actions` array defines the sequence of actions to perform. Each action is an object with a `type` and `params`.

```autohotkey
global actions := []

; Example actions (modify these for your specific task)
actions.Push({type: "activate", params: {window: targetWindow}})
actions.Push({type: "wait", params: {time: 1000}})
actions.Push({type: "click", params: {x: 500, y: 500}})
actions.Push({type: "type", params: {text: "Hello World"}})
actions.Push({type: "hotkey", params: {keys: "^a"}})  ; Ctrl+A
```

## Action Types

The script supports the following action types:

1. **activate**: Activate a window
   - `window`: Window title to activate

2. **click**: Click at specific coordinates
   - `x`: X coordinate
   - `y`: Y coordinate

3. **type**: Type text
   - `text`: Text to type

4. **hotkey**: Send a keyboard shortcut
   - `keys`: Keys to send (e.g., "^a" for Ctrl+A)

5. **wait**: Wait for a specific time
   - `time`: Time to wait in milliseconds

## Finding Mouse Coordinates

To find the coordinates for clicking:

1. Position your mouse where you want the script to click
2. Press **Ctrl+Alt+P**
3. The coordinates will be copied to your clipboard
4. Use these coordinates in your click actions

## Example: Automating Form Filling

```autohotkey
; Activate the form window
actions.Push({type: "activate", params: {window: "Form Window"}})
actions.Push({type: "wait", params: {time: 1000}})

; Click on the first field
actions.Push({type: "click", params: {x: 300, y: 200}})
actions.Push({type: "type", params: {text: "John Doe"}})

; Tab to the next field
actions.Push({type: "hotkey", params: {keys: "{Tab}"}})
actions.Push({type: "type", params: {text: "<EMAIL>"}})

; Click the submit button
actions.Push({type: "click", params: {x: 400, y: 500}})
```

## Troubleshooting

- If the automation is too fast, increase the `actionDelay` value
- If clicks are missing their targets, use the Ctrl+Alt+P hotkey to verify coordinates
- If the script doesn't activate the correct window, make sure the `targetWindow` value matches part of your window title

## Advanced Customization

For more advanced automation, you can modify the script to add new action types or enhance existing ones. The script is designed to be easily extensible.
