// Simple test script to interact with the Terminator MCP agent
const fetch = require('node-fetch');

// Function to make a request to the Terminator server
async function makeRequest(endpoint, data = {}) {
  try {
    const response = await fetch(`http://localhost:9375${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error);
    throw error;
  }
}

// Test 1: Explore the screen
async function exploreScreen() {
  console.log('Test 1: Exploring screen...');
  try {
    const result = await makeRequest('/explore_screen');
    console.log(`Found ${result.children.length} top-level elements`);
    console.log('First few elements:');
    result.children.slice(0, 3).forEach((child, index) => {
      console.log(`  ${index + 1}. ${child.role || 'unknown'}: ${child.label || 'no label'} (${child.suggested_selector})`);
    });
    return result;
  } catch (error) {
    console.error('Failed to explore screen:', error);
  }
}

// Test 2: Find a specific window
async function findWindow(titleContains) {
  console.log(`\nTest 2: Finding window containing "${titleContains}"...`);
  try {
    const result = await makeRequest('/find_window', {
      titleContains,
      timeout_ms: 5000
    });
    if (result.error) {
      console.log(`Could not find window: ${result.error}`);
      return null;
    }
    console.log(`Found window: ${result.label || 'no label'} (${result.suggested_selector})`);
    return result;
  } catch (error) {
    console.error(`Failed to find window "${titleContains}":`, error);
    return null;
  }
}

// Test 3: Capture screen with OCR
async function captureScreen() {
  console.log('\nTest 3: Capturing screen with OCR...');
  try {
    const result = await makeRequest('/capture_screen');
    if (result.error) {
      console.log(`Error capturing screen: ${result.error}`);
      return null;
    }
    console.log('OCR Text (first 150 chars):');
    console.log(result.text.substring(0, 150) + '...');
    return result;
  } catch (error) {
    console.error('Failed to capture screen:', error);
    return null;
  }
}

// Run all tests
async function runTests() {
  try {
    // Test basic screen exploration
    const screenElements = await exploreScreen();
    
    // Test finding specific windows
    const notepadWindow = await findWindow('Notepad');
    const vscodeWindow = await findWindow('Visual Studio Code');
    const browserWindow = await findWindow('Chrome');
    
    // Test OCR capability
    const ocrResult = await captureScreen();
    
    console.log('\nAll tests completed!');
  } catch (error) {
    console.error('Test suite failed:', error);
  }
}

// Run the tests
runTests();
