# FastTube - YouTube Player

A fast, lightweight YouTube player with built-in ad blocking, designed for optimal video playback performance.

## Features

- **Ad-Free Experience**: Built-in ad blocking for uninterrupted viewing
- **Fast Video Loading**: Optimized for quick video playback
- **High-Quality Playback**: Support for various video qualities up to 1080p
- **Search Functionality**: Search YouTube directly from the app
- **Watch History**: Keep track of videos you've watched
- **Simple Interface**: Clean, distraction-free UI focused on video content
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.8 or higher
- mpv player (https://mpv.io)
- PyQt6
- python-mpv
- yt-dlp
- requests

## Installation

### Automatic Installation

1. Make sure you have Python 3.8+ and mpv player installed
2. Run the setup script:

```
python fasttube_setup.py
```

This will:
- Check for mpv player installation
- Install all required Python dependencies
- Create a desktop shortcut
- Create a launcher script

### Manual Installation

1. Install mpv player from https://mpv.io
2. Install the required Python packages:

```
pip install PyQt6 python-mpv yt-dlp requests
```

3. Run the player:

```
python youtube_player.py
```

## Usage

### Starting the App

- Use the desktop shortcut created during installation, or
- Run the launcher script (`launch_fasttube.bat` on Windows or `launch_fasttube.sh` on macOS/Linux), or
- Run directly with Python: `python youtube_player.py`

### Playing Videos

1. **Search for Videos**:
   - Enter a search term in the search bar and press Enter or click "Search"
   - Click on a video from the search results to play it

2. **Play from URL**:
   - Enter a YouTube URL in the search bar and press Enter
   - The video will load and play automatically

3. **Playback Controls**:
   - Play/Pause: Click the Play/Pause button
   - Seek: Drag the position slider
   - Volume: Adjust using the volume slider
   - Quality: Select video quality from the dropdown in the toolbar

### Keyboard Shortcuts

- **Space**: Play/Pause
- **Left/Right Arrow**: Seek backward/forward
- **Up/Down Arrow**: Volume up/down
- **F**: Toggle fullscreen
- **Esc**: Exit fullscreen

## How It Works

FastTube uses several technologies to provide a fast, ad-free YouTube experience:

1. **yt-dlp**: A powerful YouTube downloader that extracts video URLs while bypassing ads
2. **mpv player**: A high-performance video player with excellent format support
3. **PyQt6**: Provides the user interface framework

The application:
1. Extracts direct video URLs from YouTube using yt-dlp
2. Plays the video directly using mpv, bypassing YouTube's web player
3. This approach avoids ads and provides faster loading times

## Troubleshooting

### Common Issues

1. **Videos not playing**:
   - Make sure mpv is installed correctly
   - Check your internet connection
   - Try a different video quality

2. **Slow performance**:
   - Lower the video quality
   - Close other applications using bandwidth
   - Check your internet speed

3. **Missing dependencies**:
   - Run the setup script again
   - Manually install the required packages

### Getting Help

If you encounter any issues, please:
1. Check the troubleshooting section above
2. Make sure all dependencies are correctly installed
3. Try running the application from the command line to see any error messages

## Privacy

FastTube respects your privacy:
- No data is sent to YouTube beyond what's necessary to fetch videos
- Watch history is stored locally only
- No tracking or analytics

## Legal Disclaimer

This application is for educational purposes only. Users are responsible for complying with YouTube's Terms of Service. The developers do not encourage or condone any violation of these terms.

## License

This project is open source and available under the MIT License.

---

Created for fast, ad-free YouTube viewing
