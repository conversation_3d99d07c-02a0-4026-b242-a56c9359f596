name: Build & Release Windows Server

on:
  push:
    tags:
      - 'v*.*.*' # Trigger on version tags like v1.0.0
  workflow_dispatch: # Allow manual triggering

jobs:
  build-release:
    name: Build & Release (Windows)
    runs-on: windows-latest
    permissions:
      contents: write # Needed to create releases

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
          target: x86_64-pc-windows-msvc

      - name: Build server
        run: cargo build --verbose --release --package server --target x86_64-pc-windows-msvc
        env:
          CARGO_TERM_COLOR: always

      - name: Package executable
        shell: pwsh
        run: |
          $exePath = "target/x86_64-pc-windows-msvc/release/server.exe"
          $zipPath = "terminator-server-windows-x86_64.zip"
          Compress-Archive -Path $exePath -DestinationPath $zipPath
          echo "ASSET_PATH=$zipPath" >> $env:GITHUB_ENV
          echo "ASSET_NAME=$zipPath" >> $env:GITHUB_ENV

      - name: Upload Artifact (Manual Trigger)
        if: github.event_name == 'workflow_dispatch'
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.ASSET_NAME }}
          path: ${{ env.ASSET_PATH }}

      - name: Create Release and Upload Asset (Tag Trigger)
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v2
        with:
          files: ${{ env.ASSET_PATH }}
          # Use the tag name for the release name
          name: Release ${{ github.ref_name }}
          # Automatically generate release notes from commits since the last tag
          generate_release_notes: true 