# Script to fix the numbering of image files in the MV Morning folder
$folderPath = "C:\Users\<USER>\Desktop\MV Morning"

# Get all image files in the folder
$imageFiles = Get-ChildItem -Path $folderPath -Filter "*.jpg" | Sort-Object {[int]($_.Name -replace '\.jpg$', '')}

# Count the actual number of files
$fileCount = $imageFiles.Count
Write-Host "Found $fileCount image files in the folder" -ForegroundColor Cyan

# Create a temporary folder for the renaming process
$tempFolderPath = Join-Path -Path $folderPath -ChildPath "temp"
if (-not (Test-Path -Path $tempFolderPath)) {
    New-Item -Path $tempFolderPath -ItemType Directory | Out-Null
    Write-Host "Created temporary folder for renaming process" -ForegroundColor Gray
}

# First, copy all files to temp folder with temp names to avoid conflicts
$counter = 1
foreach ($file in $imageFiles) {
    $tempName = "temp_$counter.jpg"
    $tempPath = Join-Path -Path $tempFolderPath -ChildPath $tempName
    
    Write-Host "Copying $($file.Name) to $tempName" -ForegroundColor Gray
    Copy-Item -Path $file.FullName -Destination $tempPath
    
    $counter++
}

# Now, copy back with the correct sequential names
$counter = 1
$tempFiles = Get-ChildItem -Path $tempFolderPath -Filter "temp_*.jpg" | Sort-Object Name
foreach ($file in $tempFiles) {
    $newName = "$counter.jpg"
    $newPath = Join-Path -Path $folderPath -ChildPath $newName
    
    # Remove the original file if it exists
    if (Test-Path -Path $newPath) {
        Remove-Item -Path $newPath -Force
    }
    
    # Copy the temp file back with the new name
    Write-Host "Renaming to $newName" -ForegroundColor Green
    Copy-Item -Path $file.FullName -Destination $newPath
    
    $counter++
}

# Clean up - remove the temp folder
Remove-Item -Path $tempFolderPath -Recurse -Force
Write-Host "Removed temporary folder" -ForegroundColor Gray

Write-Host "`nReordered $($counter - 1) files successfully!" -ForegroundColor Cyan
