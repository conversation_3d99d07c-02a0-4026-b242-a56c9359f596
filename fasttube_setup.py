#!/usr/bin/env python3
"""
FastTube Setup Script
This script installs the required dependencies for the FastTube YouTube player.
"""

import os
import sys
import subprocess
import platform
import shutil

def print_header():
    """Print the setup header"""
    print("=" * 60)
    print("FastTube - YouTube Player Setup")
    print("A Fast YouTube Player with Ad Blocking")
    print("=" * 60)
    print()

def check_python_version():
    """Check if the Python version is compatible"""
    print("Checking Python version...")
    major, minor = sys.version_info[:2]
    if major < 3 or (major == 3 and minor < 8):
        print(f"Error: Python 3.8 or higher is required. You have Python {major}.{minor}.")
        return False
    print(f"Python {major}.{minor} detected. ✓")
    return True

def check_mpv_installed():
    """Check if mpv is installed"""
    print("\nChecking for mpv installation...")
    
    if platform.system() == "Windows":
        # Check if mpv is in PATH
        mpv_in_path = shutil.which("mpv.exe") is not None
        
        if not mpv_in_path:
            # Check common installation locations
            common_paths = [
                os.path.join(os.environ.get("ProgramFiles", "C:\\Program Files"), "mpv"),
                os.path.join(os.environ.get("ProgramFiles(x86)", "C:\\Program Files (x86)"), "mpv"),
                os.path.join(os.path.expanduser("~"), "AppData", "Local", "mpv"),
            ]
            
            for path in common_paths:
                if os.path.exists(os.path.join(path, "mpv.exe")):
                    mpv_in_path = True
                    break
        
        if not mpv_in_path:
            print("mpv not found. You need to install mpv player.")
            print("Download mpv from: https://mpv.io/installation/")
            print("Make sure to add mpv to your PATH or install it to the default location.")
            return False
            
    elif platform.system() == "Linux":
        # Check if mpv is installed on Linux
        try:
            subprocess.check_call(["which", "mpv"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            print("mpv not found. You need to install mpv player.")
            print("On Ubuntu/Debian: sudo apt install mpv")
            print("On Fedora: sudo dnf install mpv")
            print("On Arch: sudo pacman -S mpv")
            return False
            
    elif platform.system() == "Darwin":  # macOS
        # Check if mpv is installed on macOS
        try:
            subprocess.check_call(["which", "mpv"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            print("mpv not found. You need to install mpv player.")
            print("Using Homebrew: brew install mpv")
            return False
    
    print("mpv player found. ✓")
    return True

def install_dependencies():
    """Install the required Python dependencies"""
    print("\nInstalling required Python dependencies...")
    
    # List of required packages
    packages = [
        "PyQt6",
        "python-mpv",
        "yt-dlp",
        "requests",
    ]
    
    try:
        # Install packages using pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        for package in packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print("All dependencies installed successfully. ✓")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False

def create_desktop_shortcut():
    """Create a desktop shortcut for the application"""
    print("\nCreating desktop shortcut...")
    
    try:
        # Get the path to the script
        script_path = os.path.abspath("youtube_player.py")
        
        if platform.system() == "Windows":
            # Windows shortcut
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop_path, "FastTube.lnk")
            
            # Create shortcut using PowerShell
            ps_script = f"""
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
            $Shortcut.TargetPath = "{sys.executable}"
            $Shortcut.Arguments = "{script_path}"
            $Shortcut.WorkingDirectory = "{os.path.dirname(script_path)}"
            $Shortcut.Description = "FastTube - YouTube Player"
            $Shortcut.Save()
            """
            
            with open("create_shortcut.ps1", "w") as f:
                f.write(ps_script)
                
            subprocess.call(["powershell", "-ExecutionPolicy", "Bypass", "-File", "create_shortcut.ps1"])
            os.remove("create_shortcut.ps1")
            
        elif platform.system() == "Linux":
            # Linux .desktop file
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            desktop_file_path = os.path.join(desktop_path, "fasttube.desktop")
            
            desktop_content = f"""[Desktop Entry]
Type=Application
Name=FastTube
Comment=Fast YouTube Player with Ad Blocking
Exec={sys.executable} {script_path}
Terminal=false
Categories=AudioVideo;Video;Player;
"""
            
            with open(desktop_file_path, "w") as f:
                f.write(desktop_content)
                
            # Make it executable
            os.chmod(desktop_file_path, 0o755)
            
        elif platform.system() == "Darwin":  # macOS
            # macOS .command file
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            command_file_path = os.path.join(desktop_path, "FastTube.command")
            
            command_content = f"""#!/bin/bash
cd "{os.path.dirname(script_path)}"
"{sys.executable}" "{script_path}"
"""
            
            with open(command_file_path, "w") as f:
                f.write(command_content)
                
            # Make it executable
            os.chmod(command_file_path, 0o755)
            
        print("Desktop shortcut created successfully. ✓")
        return True
    except Exception as e:
        print(f"Error creating desktop shortcut: {e}")
        return False

def create_launcher_script():
    """Create a launcher script for the application"""
    print("\nCreating launcher script...")
    
    try:
        # Get the path to the script
        script_path = os.path.abspath("youtube_player.py")
        
        if platform.system() == "Windows":
            # Windows batch file
            with open("launch_fasttube.bat", "w") as f:
                f.write(f'@echo off\n"{sys.executable}" "{script_path}"\n')
                
        else:
            # Unix shell script
            with open("launch_fasttube.sh", "w") as f:
                f.write(f'#!/bin/bash\n"{sys.executable}" "{script_path}"\n')
                
            # Make it executable
            os.chmod("launch_fasttube.sh", 0o755)
            
        print("Launcher script created successfully. ✓")
        return True
    except Exception as e:
        print(f"Error creating launcher script: {e}")
        return False

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Check for mpv
    if not check_mpv_installed():
        print("\nPlease install mpv player before continuing.")
        print("After installing mpv, run this setup script again.")
        input("Press Enter to exit...")
        return
    
    # Install dependencies
    if not install_dependencies():
        input("Failed to install dependencies. Press Enter to exit...")
        return
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    # Create launcher script
    create_launcher_script()
    
    print("\n" + "=" * 60)
    print("Setup completed successfully!")
    print("You can now run FastTube using the desktop shortcut")
    print("or by running the launcher script.")
    print("=" * 60)
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
