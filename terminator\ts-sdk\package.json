{"name": "desktop-use", "version": "1.0.6", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["dist"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.14.0", "typescript": "^5.6.3"}}