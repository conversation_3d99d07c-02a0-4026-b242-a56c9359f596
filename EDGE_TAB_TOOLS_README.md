# Microsoft Edge Tab Tools

This folder contains tools to help you view information about open tabs in Microsoft Edge.

## Option 1: Edge Task Manager (Easiest Method)

The simplest way to see what tabs are open in Edge is to use the built-in Task Manager:

1. Open Microsoft Edge
2. Press `Shift+Esc` on your keyboard
   - OR click the "..." menu → More tools → Browser task manager
3. This will show you all open tabs and their resource usage

## Option 2: PowerShell Scripts

This folder contains PowerShell scripts to try to extract tab information:

### get_current_tab.ps1

This script attempts to get the URL of the currently active tab by:
1. Activating the Edge window
2. Sending keyboard shortcuts to select and copy the URL
3. Reading the URL from the clipboard

To use:
```
powershell -ExecutionPolicy Bypass -File get_current_tab.ps1
```

### get_edge_tabs.ps1

This more comprehensive script tries to:
1. Find all Edge windows
2. Use UI Automation to identify tabs
3. Extract tab titles and other information

To use:
```
powershell -ExecutionPolicy Bypass -File get_edge_tabs.ps1
```

Note: These scripts may have limited success due to security restrictions in Windows.

## Option 3: Browser Extension (Most Comprehensive)

For the most detailed information about tabs, you can install the included extension:

1. Open Microsoft Edge
2. Go to `edge://extensions/`
3. Enable "Developer mode" (toggle in the bottom-left)
4. Click "Load unpacked"
5. Select the folder containing these files
6. Click on the extension icon in the toolbar to see all open tabs

### Files included for the extension:
- `edge_tab_lister.html` - The main extension interface
- `manifest.json` - Required configuration file
- `icon16.png`, `icon48.png`, `icon128.png` - Extension icons

## Limitations

Due to security restrictions in modern browsers and operating systems:
- Scripts may not be able to access all tab information
- Some approaches may require user interaction
- The browser extension approach requires manual installation

The most reliable method is using Edge's built-in Task Manager (Shift+Esc).
