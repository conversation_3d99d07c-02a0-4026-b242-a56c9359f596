// Test script to interact with a web browser
const fetch = require('node-fetch');

// Function to make a request to the Terminator server
async function makeRequest(endpoint, data = {}) {
  try {
    const response = await fetch(`http://localhost:9375${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error making request to ${endpoint}:`, error);
    throw error;
  }
}

// Run a command to open a browser
async function openBrowser(url) {
  console.log(`Opening browser with URL: ${url}...`);
  try {
    const result = await makeRequest('/run_command', {
      windowsCommand: `start ${url}`,
      unixCommand: `xdg-open ${url}`
    });
    
    console.log('Browser launch result:', result);
    return result;
  } catch (error) {
    console.error('Error opening browser:', error);
    return null;
  }
}

// Find browser window
async function findBrowserWindow(browserName) {
  console.log(`Finding ${browserName} window...`);
  try {
    // Wait a moment for the browser to open
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const result = await makeRequest('/find_window', {
      titleContains: browserName,
      timeout_ms: 5000
    });
    
    if (!result || result.error) {
      console.error(`Could not find ${browserName} window`);
      return null;
    }
    
    console.log(`Found ${browserName} window:`, result);
    return result;
  } catch (error) {
    console.error(`Error finding ${browserName}:`, error);
    return null;
  }
}

// Capture screen to see what's visible
async function captureScreen() {
  console.log('Capturing screen...');
  try {
    const result = await makeRequest('/capture_screen');
    
    if (!result || result.error) {
      console.error('Could not capture screen');
      return null;
    }
    
    console.log('Screen capture OCR text (first 150 chars):', result.text.substring(0, 150) + '...');
    return result;
  } catch (error) {
    console.error('Error capturing screen:', error);
    return null;
  }
}

// Main function
async function main() {
  try {
    // Open browser with Google
    await openBrowser('https://www.google.com');
    
    // Find browser window (try different browser names)
    let browserWindow = await findBrowserWindow('Chrome');
    if (!browserWindow) {
      browserWindow = await findBrowserWindow('Edge');
    }
    if (!browserWindow) {
      browserWindow = await findBrowserWindow('Firefox');
    }
    
    if (!browserWindow) {
      console.log('Could not find browser window. Capturing screen to see what\'s visible...');
      await captureScreen();
      return;
    }
    
    // Capture screen to see what's visible
    await captureScreen();
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
main();
