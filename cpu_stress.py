import multiprocessing
import time

def burn_cpu(duration):
    start_time = time.time()
    while time.time() - start_time < duration:
        pass  # Hog CPU for the specified duration

if __name__ == "__main__":
    duration = 10  # 10 seconds
    num_cores = multiprocessing.cpu_count()
    processes = []
    
    print(f"Starting CPU stress test on {num_cores} cores for {duration} seconds...")
    
    for _ in range(num_cores):
        p = multiprocessing.Process(target=burn_cpu, args=(duration,))
        p.start()
        processes.append(p)
    
    for p in processes:
        p.join()
    
    print("CPU stress test completed.")
