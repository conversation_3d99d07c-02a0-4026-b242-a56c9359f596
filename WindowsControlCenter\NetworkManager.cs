using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace WindowsControlCenter
{
    public class NetworkManager
    {
        // Check if the application is running with admin privileges
        public static bool IsAdministrator()
        {
            WindowsIdentity identity = WindowsIdentity.GetCurrent();
            WindowsPrincipal principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        // Get all network adapters
        public static List<NetworkAdapterInfo> GetNetworkAdapters()
        {
            List<NetworkAdapterInfo> adapters = new List<NetworkAdapterInfo>();
            
            try
            {
                // Get all network interfaces
                NetworkInterface[] networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
                
                foreach (NetworkInterface ni in networkInterfaces)
                {
                    // Skip loopback adapters
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Loopback)
                        continue;
                    
                    // Get IPv4 address
                    string ipv4Address = "Not available";
                    string subnetMask = "Not available";
                    string gateway = "Not available";
                    
                    foreach (UnicastIPAddressInformation ip in ni.GetIPProperties().UnicastAddresses)
                    {
                        if (ip.Address.AddressFamily == AddressFamily.InterNetwork)
                        {
                            ipv4Address = ip.Address.ToString();
                            subnetMask = ip.IPv4Mask.ToString();
                            break;
                        }
                    }
                    
                    // Get gateway
                    var gateways = ni.GetIPProperties().GatewayAddresses;
                    if (gateways.Count > 0)
                    {
                        gateway = gateways[0].Address.ToString();
                    }
                    
                    // Get DNS servers
                    var dnsServers = ni.GetIPProperties().DnsAddresses;
                    string dnsServerList = string.Join(", ", dnsServers.Select(dns => dns.ToString()));
                    
                    // Get MAC address
                    string macAddress = string.Join(":", ni.GetPhysicalAddress()
                        .GetAddressBytes()
                        .Select(b => b.ToString("X2")));
                    
                    // Get connection speed
                    string speed = (ni.Speed / 1000000.0).ToString("F1") + " Mbps";
                    
                    // Create adapter info
                    NetworkAdapterInfo adapter = new NetworkAdapterInfo
                    {
                        Id = ni.Id,
                        Name = ni.Name,
                        Description = ni.Description,
                        Type = ni.NetworkInterfaceType.ToString(),
                        Status = ni.OperationalStatus.ToString(),
                        MacAddress = macAddress,
                        IPv4Address = ipv4Address,
                        SubnetMask = subnetMask,
                        Gateway = gateway,
                        DnsServers = dnsServerList,
                        Speed = speed,
                        IsEnabled = (ni.OperationalStatus == OperationalStatus.Up)
                    };
                    
                    adapters.Add(adapter);
                }
                
                return adapters;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting network adapters: {ex.Message}");
                return new List<NetworkAdapterInfo>();
            }
        }
        
        // Enable or disable a network adapter
        public static async Task<bool> SetNetworkAdapterStatusAsync(string adapterId, bool enable)
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to change network adapter status.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    using (ManagementClass networkConfigClass = new ManagementClass("Win32_NetworkAdapter"))
                    {
                        foreach (ManagementObject networkAdapter in networkConfigClass.GetInstances())
                        {
                            string id = networkAdapter["DeviceID"]?.ToString();
                            string guid = networkAdapter["GUID"]?.ToString();
                            
                            if (guid == adapterId || id == adapterId)
                            {
                                if (enable)
                                {
                                    networkAdapter.InvokeMethod("Enable", null);
                                }
                                else
                                {
                                    networkAdapter.InvokeMethod("Disable", null);
                                }
                                
                                return true;
                            }
                        }
                    }
                    
                    return false;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error setting network adapter status: {ex.Message}");
                    throw;
                }
            });
        }
        
        // Release and renew DHCP lease
        public static async Task<bool> RenewDhcpLeaseAsync(string adapterId)
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to renew DHCP lease.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    // Find the adapter name from its ID
                    string adapterName = null;
                    foreach (var adapter in GetNetworkAdapters())
                    {
                        if (adapter.Id == adapterId)
                        {
                            adapterName = adapter.Name;
                            break;
                        }
                    }
                    
                    if (string.IsNullOrEmpty(adapterName))
                        return false;
                    
                    // Execute ipconfig /release and /renew commands
                    ProcessStartInfo releaseInfo = new ProcessStartInfo
                    {
                        FileName = "ipconfig",
                        Arguments = $"/release \"{adapterName}\"",
                        CreateNoWindow = true,
                        UseShellExecute = false,
                        RedirectStandardOutput = true
                    };
                    
                    Process releaseProcess = Process.Start(releaseInfo);
                    releaseProcess.WaitForExit();
                    
                    ProcessStartInfo renewInfo = new ProcessStartInfo
                    {
                        FileName = "ipconfig",
                        Arguments = $"/renew \"{adapterName}\"",
                        CreateNoWindow = true,
                        UseShellExecute = false,
                        RedirectStandardOutput = true
                    };
                    
                    Process renewProcess = Process.Start(renewInfo);
                    renewProcess.WaitForExit();
                    
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error renewing DHCP lease: {ex.Message}");
                    throw;
                }
            });
        }
        
        // Flush DNS cache
        public static async Task<bool> FlushDnsAsync()
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to flush DNS cache.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    ProcessStartInfo processInfo = new ProcessStartInfo
                    {
                        FileName = "ipconfig",
                        Arguments = "/flushdns",
                        CreateNoWindow = true,
                        UseShellExecute = false,
                        RedirectStandardOutput = true
                    };
                    
                    Process process = Process.Start(processInfo);
                    process.WaitForExit();
                    
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error flushing DNS cache: {ex.Message}");
                    throw;
                }
            });
        }
        
        // Get network statistics
        public static NetworkStatistics GetNetworkStatistics()
        {
            NetworkStatistics stats = new NetworkStatistics();
            
            try
            {
                // Get all network interfaces
                NetworkInterface[] networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
                
                // Calculate total bytes sent/received
                foreach (NetworkInterface ni in networkInterfaces)
                {
                    if (ni.OperationalStatus == OperationalStatus.Up)
                    {
                        IPv4InterfaceStatistics interfaceStats = ni.GetIPv4Statistics();
                        stats.TotalBytesSent += interfaceStats.BytesSent;
                        stats.TotalBytesReceived += interfaceStats.BytesReceived;
                        stats.TotalPacketsSent += interfaceStats.OutgoingPackets;
                        stats.TotalPacketsReceived += interfaceStats.IncomingPackets;
                        stats.TotalErrors += interfaceStats.IncomingPacketsDiscarded + interfaceStats.OutgoingPacketsDiscarded;
                    }
                }
                
                // Get active TCP connections
                IPGlobalProperties properties = IPGlobalProperties.GetIPGlobalProperties();
                TcpConnectionInformation[] connections = properties.GetActiveTcpConnections();
                stats.ActiveConnections = connections.Length;
                
                return stats;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting network statistics: {ex.Message}");
                return stats;
            }
        }
        
        // Ping a host
        public static async Task<PingResult> PingHostAsync(string host, int timeout = 1000, int count = 4)
        {
            PingResult result = new PingResult
            {
                Host = host,
                Successful = 0,
                Failed = 0,
                AverageTime = 0,
                MinTime = long.MaxValue,
                MaxTime = 0
            };
            
            try
            {
                Ping ping = new Ping();
                long totalTime = 0;
                
                for (int i = 0; i < count; i++)
                {
                    try
                    {
                        PingReply reply = await ping.SendPingAsync(host, timeout);
                        
                        if (reply.Status == IPStatus.Success)
                        {
                            result.Successful++;
                            totalTime += reply.RoundtripTime;
                            
                            if (reply.RoundtripTime < result.MinTime)
                                result.MinTime = reply.RoundtripTime;
                            
                            if (reply.RoundtripTime > result.MaxTime)
                                result.MaxTime = reply.RoundtripTime;
                        }
                        else
                        {
                            result.Failed++;
                        }
                        
                        // Add individual ping result
                        result.PingResults.Add(new PingResultItem
                        {
                            Status = reply.Status.ToString(),
                            Time = reply.RoundtripTime,
                            TTL = reply.Options?.Ttl ?? 0
                        });
                    }
                    catch
                    {
                        result.Failed++;
                        result.PingResults.Add(new PingResultItem
                        {
                            Status = "Failed",
                            Time = 0,
                            TTL = 0
                        });
                    }
                }
                
                if (result.Successful > 0)
                {
                    result.AverageTime = totalTime / result.Successful;
                }
                
                if (result.MinTime == long.MaxValue)
                {
                    result.MinTime = 0;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error pinging host: {ex.Message}");
                result.ErrorMessage = ex.Message;
                return result;
            }
        }
    }
    
    public class NetworkAdapterInfo
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string Status { get; set; }
        public string MacAddress { get; set; }
        public string IPv4Address { get; set; }
        public string SubnetMask { get; set; }
        public string Gateway { get; set; }
        public string DnsServers { get; set; }
        public string Speed { get; set; }
        public bool IsEnabled { get; set; }
    }
    
    public class NetworkStatistics
    {
        public long TotalBytesSent { get; set; }
        public long TotalBytesReceived { get; set; }
        public long TotalPacketsSent { get; set; }
        public long TotalPacketsReceived { get; set; }
        public long TotalErrors { get; set; }
        public int ActiveConnections { get; set; }
        
        // Formatted properties for display
        public string BytesSentFormatted => FormatBytes(TotalBytesSent);
        public string BytesReceivedFormatted => FormatBytes(TotalBytesReceived);
        
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            double number = bytes;
            
            while (number >= 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:N2} {suffixes[counter]}";
        }
    }
    
    public class PingResult
    {
        public string Host { get; set; }
        public int Successful { get; set; }
        public int Failed { get; set; }
        public long AverageTime { get; set; }
        public long MinTime { get; set; }
        public long MaxTime { get; set; }
        public string ErrorMessage { get; set; }
        public List<PingResultItem> PingResults { get; set; } = new List<PingResultItem>();
    }
    
    public class PingResultItem
    {
        public string Status { get; set; }
        public long Time { get; set; }
        public int TTL { get; set; }
    }
}
