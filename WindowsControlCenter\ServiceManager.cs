using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.ServiceProcess;
using System.Threading.Tasks;
using System.Management;
using System.Security.Principal;

namespace WindowsControlCenter
{
    public class ServiceManager
    {
        // Check if the application is running with admin privileges
        public static bool IsAdministrator()
        {
            WindowsIdentity identity = WindowsIdentity.GetCurrent();
            WindowsPrincipal principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        // Get all services
        public static List<ServiceInfo> GetAllServices()
        {
            List<ServiceInfo> services = new List<ServiceInfo>();
            
            try
            {
                ServiceController[] serviceControllers = ServiceController.GetServices();
                
                foreach (ServiceController sc in serviceControllers)
                {
                    string description = GetServiceDescription(sc.ServiceName);
                    string startupType = GetServiceStartupType(sc.ServiceName);
                    
                    services.Add(new ServiceInfo
                    {
                        Name = sc.ServiceName,
                        DisplayName = sc.DisplayName,
                        Status = sc.Status.ToString(),
                        StartupType = startupType,
                        Description = description
                    });
                }
                
                // Sort by display name
                return services.OrderBy(s => s.DisplayName).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting services: {ex.Message}");
                return new List<ServiceInfo>();
            }
        }
        
        // Get service description
        private static string GetServiceDescription(string serviceName)
        {
            try
            {
                using (ManagementObject service = new ManagementObject($"Win32_Service.Name='{serviceName}'"))
                {
                    service.Get();
                    return service["Description"]?.ToString() ?? "";
                }
            }
            catch
            {
                return "";
            }
        }
        
        // Get service startup type
        private static string GetServiceStartupType(string serviceName)
        {
            try
            {
                using (ManagementObject service = new ManagementObject($"Win32_Service.Name='{serviceName}'"))
                {
                    service.Get();
                    string startMode = service["StartMode"]?.ToString() ?? "";
                    
                    switch (startMode.ToLower())
                    {
                        case "auto":
                            return "Automatic";
                        case "manual":
                            return "Manual";
                        case "disabled":
                            return "Disabled";
                        default:
                            return startMode;
                    }
                }
            }
            catch
            {
                return "Unknown";
            }
        }
        
        // Start a service
        public static async Task<bool> StartServiceAsync(string serviceName)
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to start services.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    using (ServiceController sc = new ServiceController(serviceName))
                    {
                        if (sc.Status != ServiceControllerStatus.Running)
                        {
                            sc.Start();
                            sc.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));
                            return sc.Status == ServiceControllerStatus.Running;
                        }
                        return true; // Already running
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error starting service {serviceName}: {ex.Message}");
                    throw;
                }
            });
        }
        
        // Stop a service
        public static async Task<bool> StopServiceAsync(string serviceName)
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to stop services.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    using (ServiceController sc = new ServiceController(serviceName))
                    {
                        if (sc.Status != ServiceControllerStatus.Stopped)
                        {
                            sc.Stop();
                            sc.WaitForStatus(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30));
                            return sc.Status == ServiceControllerStatus.Stopped;
                        }
                        return true; // Already stopped
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error stopping service {serviceName}: {ex.Message}");
                    throw;
                }
            });
        }
        
        // Restart a service
        public static async Task<bool> RestartServiceAsync(string serviceName)
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to restart services.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    using (ServiceController sc = new ServiceController(serviceName))
                    {
                        if (sc.Status == ServiceControllerStatus.Running)
                        {
                            sc.Stop();
                            sc.WaitForStatus(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30));
                        }
                        
                        sc.Start();
                        sc.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));
                        return sc.Status == ServiceControllerStatus.Running;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error restarting service {serviceName}: {ex.Message}");
                    throw;
                }
            });
        }
        
        // Change service startup type
        public static async Task<bool> ChangeStartupTypeAsync(string serviceName, string startupType)
        {
            if (!IsAdministrator())
            {
                throw new UnauthorizedAccessException("Administrator privileges required to change service configuration.");
            }
            
            return await Task.Run(() =>
            {
                try
                {
                    string startMode;
                    switch (startupType.ToLower())
                    {
                        case "automatic":
                            startMode = "Auto";
                            break;
                        case "manual":
                            startMode = "Manual";
                            break;
                        case "disabled":
                            startMode = "Disabled";
                            break;
                        default:
                            throw new ArgumentException("Invalid startup type. Valid values are: Automatic, Manual, Disabled");
                    }
                    
                    using (ManagementObject service = new ManagementObject($"Win32_Service.Name='{serviceName}'"))
                    {
                        service.Get();
                        
                        // Invoke the ChangeStartMode method
                        ManagementBaseObject inParams = service.GetMethodParameters("ChangeStartMode");
                        inParams["StartMode"] = startMode;
                        ManagementBaseObject outParams = service.InvokeMethod("ChangeStartMode", inParams, null);
                        
                        // Check the return value
                        uint returnValue = Convert.ToUInt32(outParams["ReturnValue"]);
                        return returnValue == 0;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error changing startup type for service {serviceName}: {ex.Message}");
                    throw;
                }
            });
        }
    }
    
    public class ServiceInfo
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Status { get; set; }
        public string StartupType { get; set; }
        public string Description { get; set; }
    }
}
