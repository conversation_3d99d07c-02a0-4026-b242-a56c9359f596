# EUR/USD Trading Algorithm

This algorithm analyzes EUR/USD price data using RSI and MACD indicators to generate buy and sell signals for trading opportunities.

## Features

- **Data Acquisition**: Fetches EUR/USD price data using Yahoo Finance API
- **Technical Indicators**: Calculates RSI and MACD indicators
- **Signal Generation**: Creates buy/sell signals based on indicator conditions
- **Backtesting**: Tests the strategy on historical data
- **Visualization**: Plots the price, indicators, and signals
- **Real-time Monitoring**: Provides functionality to monitor current market conditions

## Requirements

- Python 3.6+
- Required packages:
  - pandas
  - numpy
  - matplotlib
  - yfinance

## Installation

1. Clone this repository or download the files
2. Install the required packages:

```bash
pip install pandas numpy matplotlib yfinance
```

## Usage

### Basic Usage

```python
from eurusd_trading_algorithm import EURUSDTradingAlgorithm

# Create the trading algorithm
algo = EURUSDTradingAlgorithm()

# Fetch data
data = algo.fetch_data(period="6mo", interval="15m")

# Generate signals
signals = algo.generate_signals()

# Plot signals
algo.plot_signals()

# Backtest the strategy
backtest_results = algo.backtest()
```

### Real-time Market Monitoring

```python
# Monitor current market conditions
algo.monitor_current_market(interval_minutes=15)
```

## Trading Strategy

The algorithm uses a combination of RSI and MACD indicators to generate trading signals:

### Buy Signals

A buy signal is generated when:
- RSI is below the oversold threshold (default: 30) AND/OR
- MACD line crosses above the signal line

### Sell Signals

A sell signal is generated when:
- RSI is above the overbought threshold (default: 70) AND/OR
- MACD line crosses below the signal line

### Signal Strength

The algorithm calculates a signal strength (0-100) based on:
- How oversold/overbought the RSI is
- The magnitude of the MACD histogram

## Customization

You can customize the algorithm parameters when creating an instance:

```python
# Create algorithm with custom parameters
algo = EURUSDTradingAlgorithm(
    rsi_period=14,
    rsi_overbought=70,
    rsi_oversold=30,
    macd_fast=12,
    macd_slow=26,
    macd_signal=9
)
```

## Backtesting

The backtesting functionality simulates trading based on the generated signals and calculates performance metrics:

```python
# Backtest with custom parameters
backtest_results = algo.backtest(initial_capital=10000, position_size=0.1)
```

## Disclaimer

This algorithm is for educational and informational purposes only. It is not financial advice, and you should not rely on any of its outputs for your trading decisions. Trading forex carries a high level of risk and may not be suitable for all investors. Past performance is not indicative of future results.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
