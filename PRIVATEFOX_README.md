# PrivateFox Browser

A private, fast, and reliable web browser built with Python and PyQt6.

## Features

- **Built-in Ad Blocking**: Blocks common advertisements and tracking scripts
- **Tracker Blocking**: Prevents websites from tracking your browsing behavior
- **Private Browsing Mode**: Always operates in private mode with no history saved
- **Auto-Clear Data**: Automatically clears browsing data on exit or at regular intervals
- **HTTPS Enforcement**: Automatically upgrades connections to HTTPS for better security
- **Custom User Agent**: Uses a generic user agent to prevent fingerprinting
- **Fast and Lightweight**: Minimal design focused on speed and privacy
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.8 or higher
- PyQt6
- PyQt6-WebEngine

## Installation

### Automatic Installation

1. Make sure you have Python 3.8+ installed
2. Run the setup script:

```
python setup.py
```

This will:
- Install all required dependencies
- Create a desktop shortcut
- Create a launcher script

### Manual Installation

1. Install the required packages:

```
pip install PyQt6 PyQt6-WebEngine
```

2. Run the browser:

```
python private_browser.py
```

## Usage

### Starting the Browser

- Use the desktop shortcut created during installation, or
- Run the launcher script (`launch_privatefox.bat` on Windows or `launch_privatefox.sh` on macOS/Linux), or
- Run directly with Python: `python private_browser.py`

### Browser Controls

- **Navigation**: Use the back, forward, reload, and home buttons in the toolbar
- **URL/Search**: Enter URLs directly in the address bar or type search terms to search with DuckDuckGo
- **Tabs**: Create new tabs with the + button, close tabs with the X on each tab
- **Zoom**: Use Ctrl++ to zoom in, Ctrl+- to zoom out, or find these options in the View menu
- **Settings**: Access browser settings through the gear icon or the menu

### Keyboard Shortcuts

- **New Tab**: Ctrl+T
- **Close Tab**: Ctrl+W
- **Reload Page**: F5 or Ctrl+R
- **Navigate Back**: Alt+Left Arrow
- **Navigate Forward**: Alt+Right Arrow
- **Zoom In**: Ctrl++
- **Zoom Out**: Ctrl+-
- **Find in Page**: Ctrl+F
- **Quit**: Ctrl+Q

## Privacy Features

### Ad and Tracker Blocking

PrivateFox comes with built-in blocking of common advertising and tracking domains. This helps:
- Reduce page load times
- Save bandwidth
- Prevent tracking of your browsing habits
- Improve overall privacy

### Private Browsing

The browser always operates in private mode:
- No browsing history is saved
- Cookies are not stored between sessions
- Cache is cleared regularly
- No data is written to disk

### HTTPS Everywhere

All HTTP connections are automatically upgraded to HTTPS when available, providing:
- Encrypted connections
- Protection from man-in-the-middle attacks
- Better overall security

### Do Not Track

The browser sends "Do Not Track" headers with all requests, indicating to websites that you prefer not to be tracked.

## Customization

You can customize various aspects of the browser by editing the settings.json file in the .privatefox directory in your home folder:

- **Search Engine**: Change the default search engine
- **Homepage**: Set your preferred homepage
- **User Agent**: Customize your browser's user agent
- **Clear on Exit**: Enable/disable clearing data on exit

## Troubleshooting

### Common Issues

1. **Browser won't start**:
   - Make sure you have Python 3.8+ installed
   - Verify that PyQt6 and PyQt6-WebEngine are installed correctly

2. **Missing icons**:
   - The browser uses system theme icons; if they're missing, install an icon theme

3. **Websites not loading**:
   - Check your internet connection
   - Some websites may detect and block private browsers; try changing the user agent

### Getting Help

If you encounter any issues, please:
1. Check the troubleshooting section above
2. Make sure all dependencies are correctly installed
3. Try running the browser from the command line to see any error messages

## License

This project is open source and available under the MIT License.

## Disclaimer

This browser is provided as-is without any warranty. While it includes privacy features, no browser can guarantee complete anonymity or security. For maximum privacy, consider using specialized tools like Tor.

---

Created with ❤️ for privacy
