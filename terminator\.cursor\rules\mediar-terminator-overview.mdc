---
description: 
globs: 
alwaysApply: false
---
# Mediar Terminator Project Overview

Mediar Terminator is an AI-first, cross-platform UI automation engine written in Rust, designed to provide fast and reliable desktop automation capabilities.

## Core Architecture

### Server Component
- Written in Rust
- Provides the core automation engine
- Handles OS-level accessibility API interactions
- Built with async capabilities for efficient operation

### SDK Components
1. **TypeScript SDK**
   - Main client library
   - Playwright-style API
   - Located in `ts-sdk/`
   - Published to npm

2. **Python SDK**
   - Alternative client library
   - Located in `python-sdk/`
   - Published to PyPI

## Platform Support

| Platform | Status | Notes |
|----------|---------|-------|
| Windows | ✅ Primary | Full features, best performance |
| macOS | 🟡 Secondary | Core functionality available |
| Linux | ❌ Not Supported | No current plans |

## Key Technical Features

### Core Capabilities
- **Accessibility API Integration:** Uses OS-level accessibility APIs instead of computer vision
- **Background App Control:** Can interact with applications even when not in focus
- **Deep UI Introspection:** Detailed understanding of UI elements and their properties
- **Cross-Platform Design:** Unified API across supported platforms

### Performance Characteristics
- **Speed:** Optimized for fast execution
- **Reliability:** Stable and consistent behavior
- **Resource Efficiency:** Low overhead operation

### Integration Points
- **AI Agent Integration:** Designed for seamless LLM agent integration
- **External SDK Support:** Compatible with frameworks like Vercel AI
- **API Design:** Playwright-inspired interface for familiarity

## Development Guidelines

### Code Organization
- **Modular Structure:** Clear separation of concerns
- **SDK Independence:** Separate packages for different languages
- **Server-First Design:** Core functionality in Rust server

### Testing & Debugging
- **Windows Tools:**
  - Accessibility Insights for Windows
  - FlaUInspect for UI element inspection
- **Debug Mode:** Available via `--debug` flag

### Release Process
- Automated server releases via GitHub Actions
- Manual SDK releases to respective package registries
- Version tagging for coordinated releases

## Use Cases
1. PDF-to-form autofill automation
2. End-to-end application testing
3. AI-driven desktop copilots
4. Business process automation
5. Legacy application integration

## Technical Dependencies
- **Windows:** uiautomation-rs
- **macOS:** Native Accessibility API
- **Build Tools:** Rust/Cargo, npm, pip

## Project Status
- **Development Stage:** Experimental
- **Primary Focus:** Windows platform
- **Community:** Active Discord community
- **Documentation:** Available at docs.screenpi.pe/terminator
