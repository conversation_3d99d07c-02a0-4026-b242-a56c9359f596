# PowerShell Simple AI Chat Simulator

# Define some pre-programmed responses about AI
$aiResponses = @{
    "hello" = "Hello! I'm a simulated AI assistant. How can I help you with AI-related topics today?"
    "what is ai" = "Artificial Intelligence (AI) refers to systems designed to perform tasks that typically require human intelligence. These include learning, reasoning, problem-solving, perception, and language understanding."
    "types of ai" = "AI can be categorized as: 1) Narrow/Weak AI (designed for specific tasks), 2) General/Strong AI (hypothetical human-like intelligence), 3) Based on techniques (rule-based, machine learning, deep learning, etc.)"
    "machine learning" = "Machine Learning is a subset of AI that enables systems to learn from data without explicit programming. Types include supervised learning, unsupervised learning, and reinforcement learning."
    "ethics" = "AI ethics concerns include: bias and fairness, transparency, privacy, accountability, job displacement, safety, and autonomy. These require multidisciplinary approaches."
    "future of ai" = "The future of AI may include more capable foundation models, multimodal AI, personalization, human-AI collaboration, specialized hardware, democratization, regulation, and scientific breakthroughs."
    "risks" = "AI risks include bias, privacy concerns, security vulnerabilities, job displacement, power concentration, autonomous weapons, misinformation, overreliance, and long-term control challenges."
    "benefits" = "AI benefits include advances in healthcare, climate solutions, productivity, accessibility, education, scientific discovery, safety improvements, creative tools, and economic growth."
    "agi" = "Artificial General Intelligence (AGI) refers to hypothetical AI with human-like general intelligence across diverse domains. We don't have AGI today, and there's no consensus on when it might be achieved."
}

# Function to get a response
function Get-AiResponse {
    param (
        [string]$Query
    )
    
    # Convert query to lowercase for matching
    $queryLower = $Query.ToLower()
    
    # Check for exact matches
    foreach ($key in $aiResponses.Keys) {
        if ($queryLower -eq $key) {
            return $aiResponses[$key]
        }
    }
    
    # Check for partial matches
    foreach ($key in $aiResponses.Keys) {
        if ($queryLower.Contains($key) -or $key.Contains($queryLower)) {
            return $aiResponses[$key]
        }
    }
    
    # Default response if no match
    return "I'm a simulated AI assistant with limited responses. I can discuss basic AI concepts like 'what is AI', 'types of AI', 'machine learning', 'ethics', 'future of AI', 'risks', 'benefits', and 'AGI'. Please try asking about one of these topics."
}

# Main function
function Start-SimpleAiChat {
    Clear-Host
    Write-Host "========================================"
    Write-Host "       Simple AI Chat Simulator         "
    Write-Host "========================================"
    Write-Host "This is a simple simulation of an AI chat about artificial intelligence topics."
    Write-Host "Type 'exit' or 'quit' to end the conversation"
    Write-Host "Try asking about: 'what is AI', 'types of AI', 'machine learning', 'ethics',"
    Write-Host "                  'future of AI', 'risks', 'benefits', or 'AGI'"
    Write-Host "========================================"
    Write-Host ""
    
    # Initial greeting
    Write-Host "AI: Hello! I'm a simulated AI assistant focused on AI topics. How can I help you today?"
    Write-Host ""
    
    # Main chat loop
    while ($true) {
        # Get user input
        Write-Host -NoNewline "You: "
        $userInput = Read-Host
        
        # Check for exit command
        if ($userInput -eq "exit" -or $userInput -eq "quit") {
            Write-Host ""
            Write-Host "AI: Goodbye! Thanks for chatting."
            break
        }
        
        # Get and display AI response
        $response = Get-AiResponse -Query $userInput
        Write-Host ""
        Write-Host "AI: $response"
        Write-Host ""
    }
}

# Start the chat
Start-SimpleAiChat
