#!/usr/bin/env python3
"""
Terminal-based AI Chat Client using OpenRouter API
This script allows you to chat with various AI models directly from your terminal.
"""

import os
import sys
import json
import requests
from datetime import datetime
import argparse
import textwrap
import time
import colorama
from colorama import Fore, Style

# Initialize colorama
colorama.init()

# Default model to use
DEFAULT_MODEL = "anthropic/claude-3-opus"

def clear_screen():
    """Clear the terminal screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_api_key():
    """Get the OpenRouter API key from environment variable or prompt user."""
    api_key = os.environ.get("OPENROUTER_API_KEY")
    if not api_key:
        print(f"{Fore.YELLOW}OpenRouter API key not found in environment variables.{Style.RESET_ALL}")
        api_key = input("Please enter your OpenRouter API key: ")
        # Save to environment for this session
        os.environ["OPENROUTER_API_KEY"] = api_key
    return api_key

def format_message(role, content):
    """Format a message with appropriate colors."""
    if role == "assistant":
        return f"{Fore.GREEN}AI: {Style.RESET_ALL}{content}"
    elif role == "user":
        return f"{Fore.BLUE}You: {Style.RESET_ALL}{content}"
    elif role == "system":
        return f"{Fore.YELLOW}System: {Style.RESET_ALL}{content}"
    else:
        return f"{role.capitalize()}: {content}"

def wrap_text(text, width=100):
    """Wrap text to a specified width."""
    return "\n".join(textwrap.wrap(text, width=width))

def send_message(messages, model=DEFAULT_MODEL, api_key=None, stream=True):
    """Send a message to the AI model and return the response."""
    if not api_key:
        api_key = get_api_key()
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
        "HTTP-Referer": "https://terminal-chat.local",  # Required by OpenRouter
        "X-Title": "Terminal AI Chat"  # Optional, helps OpenRouter identify your app
    }
    
    data = {
        "model": model,
        "messages": messages,
        "stream": stream
    }
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    if stream:
        response = requests.post(url, headers=headers, json=data, stream=True)
        response.raise_for_status()
        
        # For streaming responses
        full_response = ""
        print(f"{Fore.GREEN}AI: {Style.RESET_ALL}", end="", flush=True)
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    line = line[6:]  # Remove 'data: ' prefix
                    if line == "[DONE]":
                        break
                    try:
                        json_data = json.loads(line)
                        if 'choices' in json_data and len(json_data['choices']) > 0:
                            delta = json_data['choices'][0].get('delta', {})
                            content = delta.get('content', '')
                            if content:
                                print(content, end="", flush=True)
                                full_response += content
                    except json.JSONDecodeError:
                        pass
        
        print()  # Add a newline at the end
        return full_response
    else:
        # For non-streaming responses
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        response_data = response.json()
        return response_data['choices'][0]['message']['content']

def save_conversation(conversation, filename=None):
    """Save the conversation to a file."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversation_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(conversation, f, indent=2)
    
    print(f"{Fore.YELLOW}Conversation saved to {filename}{Style.RESET_ALL}")

def load_conversation(filename):
    """Load a conversation from a file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"{Fore.RED}Error loading conversation: {e}{Style.RESET_ALL}")
        return []

def list_available_models(api_key=None):
    """List available models from OpenRouter."""
    if not api_key:
        api_key = get_api_key()
    
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    url = "https://openrouter.ai/api/v1/models"
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        models = response.json()['data']
        
        print(f"{Fore.YELLOW}Available Models:{Style.RESET_ALL}")
        for model in models:
            print(f"- {Fore.CYAN}{model['id']}{Style.RESET_ALL}")
            print(f"  Context: {model['context_length']} tokens")
            print(f"  Description: {model.get('description', 'No description available')}")
            print()
        
    except requests.RequestException as e:
        print(f"{Fore.RED}Error fetching models: {e}{Style.RESET_ALL}")

def main():
    """Main function to run the chat client."""
    parser = argparse.ArgumentParser(description="Terminal-based AI Chat Client")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help=f"Model to use (default: {DEFAULT_MODEL})")
    parser.add_argument("--system", type=str, help="System message to set the behavior of the assistant")
    parser.add_argument("--save", type=str, help="Save conversation to specified file")
    parser.add_argument("--load", type=str, help="Load conversation from specified file")
    parser.add_argument("--list-models", action="store_true", help="List available models")
    parser.add_argument("--no-stream", action="store_true", help="Disable streaming responses")
    
    args = parser.parse_args()
    
    if args.list_models:
        list_available_models()
        return
    
    clear_screen()
    print(f"{Fore.CYAN}========================================{Style.RESET_ALL}")
    print(f"{Fore.CYAN}       Terminal AI Chat Client         {Style.RESET_ALL}")
    print(f"{Fore.CYAN}========================================{Style.RESET_ALL}")
    print(f"Using model: {Fore.YELLOW}{args.model}{Style.RESET_ALL}")
    print(f"Type {Fore.YELLOW}'exit'{Style.RESET_ALL} or {Fore.YELLOW}'quit'{Style.RESET_ALL} to end the conversation")
    print(f"Type {Fore.YELLOW}'save'{Style.RESET_ALL} to save the conversation")
    print(f"Type {Fore.YELLOW}'clear'{Style.RESET_ALL} to clear the screen")
    print(f"{Fore.CYAN}========================================{Style.RESET_ALL}\n")
    
    # Initialize conversation
    conversation = []
    
    # Load conversation if specified
    if args.load:
        conversation = load_conversation(args.load)
        print(f"{Fore.YELLOW}Loaded conversation from {args.load}{Style.RESET_ALL}")
        # Display the loaded conversation
        for message in conversation:
            print(format_message(message["role"], message["content"]))
    
    # Add system message if provided
    if args.system:
        system_message = {"role": "system", "content": args.system}
        conversation.append(system_message)
        print(format_message("system", args.system))
    
    # Main chat loop
    while True:
        try:
            # Get user input
            user_input = input(f"{Fore.BLUE}You: {Style.RESET_ALL}")
            
            # Check for special commands
            if user_input.lower() in ['exit', 'quit']:
                break
            elif user_input.lower() == 'save':
                save_conversation(conversation, args.save)
                continue
            elif user_input.lower() == 'clear':
                clear_screen()
                continue
            
            # Add user message to conversation
            user_message = {"role": "user", "content": user_input}
            conversation.append(user_message)
            
            # Get AI response
            ai_response = send_message(conversation, model=args.model, stream=not args.no_stream)
            
            # Add AI response to conversation
            ai_message = {"role": "assistant", "content": ai_response}
            conversation.append(ai_message)
            
            # If not streaming, print the response
            if args.no_stream:
                print(format_message("assistant", wrap_text(ai_response)))
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"{Fore.RED}Error: {e}{Style.RESET_ALL}")
    
    # Save conversation if specified
    if args.save:
        save_conversation(conversation, args.save)

if __name__ == "__main__":
    main()
