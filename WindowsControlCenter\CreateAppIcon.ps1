# Create a simple app icon for the Windows Control Center
Add-Type -AssemblyName System.Drawing

function Create-Icon {
    param (
        [string]$outputPath,
        [int]$size = 256
    )
    
    $bitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Fill background with gradient
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
        [System.Drawing.Point]::new(0, 0),
        [System.Drawing.Point]::new($size, $size),
        [System.Drawing.Color]::FromArgb(44, 62, 80),  # Dark blue
        [System.Drawing.Color]::FromArgb(52, 152, 219) # Light blue
    )
    $graphics.FillRectangle($brush, 0, 0, $size, $size)
    
    # Draw a gear icon
    $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, $size * 0.05)
    $centerX = $size / 2
    $centerY = $size / 2
    $outerRadius = $size * 0.35
    $innerRadius = $size * 0.2
    $teethCount = 8
    
    $points = New-Object System.Collections.Generic.List[System.Drawing.PointF]
    
    for ($i = 0; $i -lt $teethCount * 2; $i++) {
        $angle = $i * [Math]::PI / $teethCount
        $radius = if ($i % 2 -eq 0) { $outerRadius } else { $innerRadius }
        
        $x = $centerX + $radius * [Math]::Cos($angle)
        $y = $centerY + $radius * [Math]::Sin($angle)
        
        $points.Add([System.Drawing.PointF]::new($x, $y))
    }
    
    $graphics.DrawPolygon($pen, $points.ToArray())
    
    # Draw center circle
    $centerCircleRadius = $size * 0.1
    $graphics.FillEllipse(
        [System.Drawing.Brushes]::White,
        $centerX - $centerCircleRadius,
        $centerY - $centerCircleRadius,
        $centerCircleRadius * 2,
        $centerCircleRadius * 2
    )
    
    # Save as icon
    $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
    $fileStream = New-Object System.IO.FileStream($outputPath, [System.IO.FileMode]::Create)
    $icon.Save($fileStream)
    
    # Clean up
    $fileStream.Close()
    $icon.Dispose()
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $pen.Dispose()
    
    Write-Host "Icon created at: $outputPath"
}

# Create the icon
Create-Icon -outputPath "app.ico"
